
00:00 +0: loading /Users/<USER>/repos/skills/test/features/availability/presentation/availability_screen_widget_test.dart                                                                      
00:01 +0: loading /Users/<USER>/repos/skills/test/features/availability/presentation/availability_screen_widget_test.dart                                                                      
00:02 +0: loading /Users/<USER>/repos/skills/test/features/availability/presentation/availability_screen_widget_test.dart                                                                      
00:02 +0: (setUpAll)                                                                                                                                                                                   
00:02 +0: (setUpAll)                                                                                                                                                                                   
MockSupabaseManager initialized. The manager's `client` property is now a SupabaseClient configured with mocks. Tests should use this to override relevant providers (e.g., Riverpod's supabaseClientProvider). This helper does NOT call Supabase.initialize() globally due to persistent compilation issues with its parameters. Therefore, direct use of `Supabase.instance` in application code might not use these mocks unless `Supabase.initialize` was called elsewhere with these exact mock instances (which is unlikely and hard to coordinate).
supabase.supabase_flutter: INFO: ***** Supabase init completed ***** 

00:03 +0: AvailabilityScreen Widget Tests renders loading indicator when allPitches are loading                                                                                                        
00:03 +0: AvailabilityScreen Widget Tests renders loading indicator when allPitches are loading                                                                                                        
DEBUG: [AvailabilityScreen] initState: _selectedDate initialized to: 2024-01-01 00:00:00.000

00:04 +1: AvailabilityScreen Widget Tests renders loading indicator when allPitches are loading                                                                                                        
00:04 +1: AvailabilityScreen Widget Tests renders "No pitches available" when allPitches is empty                                                                                                      
00:04 +1: AvailabilityScreen Widget Tests renders "No pitches available" when allPitches is empty                                                                                                      
DEBUG: [AvailabilityScreen] initState: _selectedDate initialized to: 2024-01-01 00:00:00.000

00:04 +2: AvailabilityScreen Widget Tests renders "No pitches available" when allPitches is empty                                                                                                      
00:04 +2: AvailabilityScreen Widget Tests selects first pitch and loads its settings and slots if selectedPitchIdProvider is null initially                                                            
00:04 +2: AvailabilityScreen Widget Tests selects first pitch and loads its settings and slots if selectedPitchIdProvider is null initially                                                            
DEBUG: [AvailabilityScreen] initState: _selectedDate initialized to: 2024-01-01 00:00:00.000
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Loading pitch settings for pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Loading slots for date 2024-01-01 00:00:00.000, pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Slots loaded for date 2024-01-01 00:00:00.000, pitch 1: 0 slots

00:04 +3: AvailabilityScreen Widget Tests selects first pitch and loads its settings and slots if selectedPitchIdProvider is null initially                                                            
00:04 +3: AvailabilityScreen Widget Tests renders loading indicator when pitch settings are loading (after pitch selection)                                                                            
00:04 +3: AvailabilityScreen Widget Tests renders loading indicator when pitch settings are loading (after pitch selection)                                                                            
DEBUG: [AvailabilityScreen] initState: _selectedDate initialized to: 2024-01-01 00:00:00.000
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Loading pitch settings for pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Loading slots for date 2024-01-01 00:00:00.000, pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Slots loaded for date 2024-01-01 00:00:00.000, pitch 1: 0 slots

00:04 +4: AvailabilityScreen Widget Tests renders loading indicator when pitch settings are loading (after pitch selection)                                                                            
00:04 +4: AvailabilityScreen Widget Tests renders loading indicator when slots are loading (after pitch and settings loaded)                                                                           
00:04 +4: AvailabilityScreen Widget Tests renders loading indicator when slots are loading (after pitch and settings loaded)                                                                           
DEBUG: [AvailabilityScreen] initState: _selectedDate initialized to: 2024-01-01 00:00:00.000
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Loading pitch settings for pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Loading slots for date 2024-01-01 00:00:00.000, pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Slots loaded for date 2024-01-01 00:00:00.000, pitch 1: 0 slots

00:04 +5: AvailabilityScreen Widget Tests renders loading indicator when slots are loading (after pitch and settings loaded)                                                                           
00:04 +5: AvailabilityScreen Widget Tests displays slot availability information correctly                                                                                                             
00:04 +5: AvailabilityScreen Widget Tests displays slot availability information correctly                                                                                                             
DEBUG: [AvailabilityScreen] initState: _selectedDate initialized to: 2024-01-01 00:00:00.000
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Loading pitch settings for pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Loading slots for date 2024-01-01 00:00:00.000, pitch 1
DEBUG: [AvailabilityDetails] Building for date: 2024-01-01 00:00:00.000, pitch: 1
DEBUG: [AvailabilityDetails] Pitch settings loaded for pitch 1: Test Pitch 1
DEBUG: [AvailabilityDetails] Slots loaded for date 2024-01-01 00:00:00.000, pitch 1: 3 slots
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync state is AsyncLoading<List<Booking>>[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync is loading.[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync state is AsyncLoading<List<Booking>>[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync is loading.[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync state is AsyncLoading<List<Booking>>[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync is loading.[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync state is AsyncData<List<Booking>>[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: upcomingBookingsCount = 0, bookingLimitReached = false[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync state is AsyncData<List<Booking>>[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: upcomingBookingsCount = 0, bookingLimitReached = false[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: userBookingsAsync state is AsyncData<List<Booking>>[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
[38;5;12m│ #0   AppLogger.i (package:skills/core/utils/logger_service.dart:34:13)[0m
[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
[38;5;12m│ 💡 SlotListItem build: upcomingBookingsCount = 0, bookingLimitReached = false[0m
[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m

00:05 +6: AvailabilityScreen Widget Tests displays slot availability information correctly                                                                                                             
00:05 +6: (tearDownAll)                                                                                                                                                                                
00:05 +6: All tests passed!                                                                                                                                                                            
