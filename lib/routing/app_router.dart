import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skills/features/auth/application/auth_service.dart';
import 'package:skills/features/auth/presentation/login_page.dart';
import 'package:skills/features/auth/presentation/sign_up_page.dart';
import 'package:skills/features/home/<USER>/home_page.dart';
import 'package:skills/features/availability/presentation/availability_screen.dart';
import 'package:skills/features/booking/presentation/my_bookings_screen.dart';
import 'package:skills/features/booking/presentation/booking_confirmation_screen.dart';
import 'package:skills/core/utils/logger_service.dart'; // Import the logger service

// Helper class to convert a Stream to a Listenable for GoRouter's refreshListenable
class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    _subscription = stream.asBroadcastStream().listen(
      (dynamic _) => notifyListeners(),
    );
  }

  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

// Provider for the GoRouterRefreshStream
final goRouterRefreshStreamProvider = Provider<GoRouterRefreshStream>((ref) {
  final authStream =
      ref
          .read(authStateChangesProvider.future)
          .asStream(); // Replace deprecated .stream with .future.asStream()
  final notifier = GoRouterRefreshStream(authStream);
  ref.onDispose(() {
    notifier.dispose();
  });
  return notifier;
});

// Provider to expose the GoRouter instance
final goRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateChangesProvider);
  // Watch the dedicated provider for the refresh notifier
  final refreshNotifier = ref.watch(goRouterRefreshStreamProvider);
  final logger = AppLogger(); // Create a logger instance

  logger.d(
    'DEBUG: goRouterProvider rebuilding. AuthState: ${authState.valueOrNull}, AuthState hasError: ${authState.hasError}, AuthState isLoading: ${authState.isLoading}',
  );

  return GoRouter(
    initialLocation: '/',
    refreshListenable: refreshNotifier, // Use the notifier from its provider
    redirect: (BuildContext context, GoRouterState state) {
      final bool loggedIn = authState.valueOrNull != null;
      // Use state.uri.toString() as it's a reliable representation of the current location.
      // state.matchedLocation can sometimes be null for the initial route or during complex redirects.
      final String currentRoute = state.uri.toString();
      logger.d(
        'DEBUG: GoRouter redirect triggered. Current route: $currentRoute, LoggedIn: $loggedIn',
      );

      final bool loggingIn =
          currentRoute == '/login' || currentRoute == '/signup';

      // If not logged in and not on login/signup, redirect to login
      if (!loggedIn && !loggingIn) {
        logger.d('DEBUG: Redirecting to /login');
        return '/login';
      }
      // If logged in and on login/signup, redirect to home
      if (loggedIn && loggingIn) {
        logger.d('DEBUG: Redirecting to /');
        return '/';
      }
      logger.d('DEBUG: No redirect needed.');
      return null; // No redirect needed
    },
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) {
          logger.d('DEBUG: Building HomePage route (/)');
          return const HomePage();
        },
        routes: [
          GoRoute(
            path: 'availability', // Accessed as /availability
            name: 'availability', // Added name for consistency, if ever needed
            builder: (context, state) {
              logger.d(
                'DEBUG: Building AvailabilityScreen route (/availability)',
              );
              // Removed pitchId logic
              return const AvailabilityScreen(); // Instantiate without pitchId
            },
          ),
          GoRoute(
            path: 'my-bookings', // Accessed as /my-bookings
            name: MyBookingsScreen.routeName, // Added name property
            builder: (context, state) {
              logger.d('DEBUG: Building MyBookingsScreen route (/my-bookings)');
              return const MyBookingsScreen();
            },
          ),
          GoRoute(
            path: BookingConfirmationScreen.routePath.substring(
              1,
            ), // Remove leading '/'
            name: BookingConfirmationScreen.routeName,
            builder: (context, state) {
              // TODO: Extract parameters safely and pass them to BookingConfirmationScreen
              // For now, using placeholders or default values
              final pitchId =
                  state.uri.queryParameters['pitchId'] ?? 'defaultPitchId';
              final pitchName =
                  state.uri.queryParameters['pitchName'] ?? 'Default Pitch';
              final selectedDateString =
                  state.uri.queryParameters['selectedDate'];
              final slotStartTimeString =
                  state.uri.queryParameters['slotStartTime'];
              final slotEndTimeString =
                  state.uri.queryParameters['slotEndTime'];

              // Basic error handling/defaults for dates - robust parsing needed
              final selectedDate =
                  selectedDateString != null
                      ? DateTime.tryParse(selectedDateString)
                      : DateTime.now();
              final slotStartTime =
                  slotStartTimeString != null
                      ? DateTime.tryParse(slotStartTimeString)
                      : DateTime.now();
              final slotEndTime =
                  slotEndTimeString != null
                      ? DateTime.tryParse(slotEndTimeString)
                      : DateTime.now().add(const Duration(hours: 1));

              if (selectedDate == null ||
                  slotStartTime == null ||
                  slotEndTime == null) {
                // Handle error: perhaps redirect to an error page or show a message
                logger.e(
                  'Error parsing date/time for BookingConfirmationScreen',
                );
                return Scaffold(
                  body: Center(
                    child: Text('Error: Invalid booking details provided.'),
                  ),
                );
              }

              logger.d(
                'DEBUG: Building BookingConfirmationScreen route (${BookingConfirmationScreen.routePath})',
              );
              return BookingConfirmationScreen(
                pitchId: pitchId,
                pitchName: pitchName,
                selectedDate: selectedDate,
                slotStartTime: slotStartTime,
                slotEndTime: slotEndTime,
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) {
          logger.d('DEBUG: Building LoginPage route (/login)');
          return const LoginPage();
        },
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) {
          logger.d('DEBUG: Building SignUpPage route (/signup)');
          return const SignUpPage();
        },
      ),
    ],
    errorBuilder:
        (context, state) => Scaffold(
          body: Center(child: Text('Page not found: ${state.error}')),
        ),
  );
});
