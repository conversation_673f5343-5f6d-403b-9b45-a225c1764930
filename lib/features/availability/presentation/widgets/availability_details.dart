import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:skills/features/availability/application/availability_service.dart';
import 'package:skills/features/availability/application/real_time_availability_provider.dart'
    as real_time;
import 'package:skills/features/availability/presentation/widgets/slot_list_item.dart';
import 'package:skills/core/utils/logger.dart';

class AvailabilityDetails extends ConsumerWidget {
  final DateTime selectedDate;
  final int selectedPitchId;

  const AvailabilityDetails({
    super.key,
    required this.selectedDate,
    required this.selectedPitchId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    logger.d(
      '[AvailabilityDetails] Building for date: $selectedDate, pitch: $selectedPitchId',
    );

    return ref
        .watch(pitchSettingsProvider(selectedPitchId))
        .when(
          data: (settings) {
            logger.d(
              '[AvailabilityDetails] Pitch settings loaded for pitch $selectedPitchId: ${settings.name}',
            );
            return ref
                .watch(
                  real_time.realTimeAvailabilityProvider(
                    real_time.RealTimeAvailabilityParams(
                      date: selectedDate,
                      pitchId: selectedPitchId,
                    ),
                  ),
                )
                .when(
                  data: (slots) {
                    logger.d(
                      '[AvailabilityDetails] Slots loaded for date $selectedDate, pitch $selectedPitchId: ${slots.length} slots',
                    );
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            'Available Slots on ${DateFormat('yyyy-MM-dd').format(selectedDate.toLocal())} for ${settings.name}:',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 4.0,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Pitch Open: ${settings.openTime}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Pitch Close: ${settings.closeTime}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Slot Duration: ${settings.slotDurationMinutes} mins',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child:
                              slots.isEmpty
                                  ? const Center(
                                    child: Text(
                                      "No slots available for this day.",
                                    ),
                                  )
                                  : ListView(
                                    children:
                                        slots.map((slot) {
                                          // Use slot.startTime as a unique key component for ValueKey
                                          return SlotListItem(
                                            key: ValueKey(
                                              '${slot.startTime.toIso8601String()}-$selectedPitchId',
                                            ),
                                            slot: slot,
                                            selectedDate: selectedDate,
                                            selectedPitchId: selectedPitchId,
                                          );
                                        }).toList(),
                                  ),
                        ),
                      ],
                    );
                  },
                  loading: () {
                    logger.d(
                      '[AvailabilityDetails] Loading slots for date $selectedDate, pitch $selectedPitchId',
                    );
                    return const Center(
                      child: CircularProgressIndicator(
                        key: Key("slots_loader"),
                      ),
                    );
                  },
                  error: (err, stack) {
                    logger.e(
                      '[AvailabilityDetails] Error fetching slots for date $selectedDate, pitch $selectedPitchId: $err',
                      error: err,
                      stackTrace: stack,
                    );
                    return Center(
                      child: Text(
                        'Error fetching available slots: ${err.toString()}',
                      ),
                    );
                  },
                );
          },
          loading: () {
            logger.d(
              '[AvailabilityDetails] Loading pitch settings for pitch $selectedPitchId',
            );
            return const Center(
              child: CircularProgressIndicator(key: Key("settings_loader")),
            );
          },
          error: (err, stack) {
            logger.e(
              '[AvailabilityDetails] Error fetching pitch settings for pitch $selectedPitchId: $err',
              error: err,
              stackTrace: stack,
            );
            return Center(
              child: Text('Error fetching pitch settings: ${err.toString()}'),
            );
          },
        );
  }
}
