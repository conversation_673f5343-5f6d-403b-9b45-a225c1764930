// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in skills/features/availability/application/availability_service.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:skills/features/availability/application/availability_service.dart'
    as _i3;
import 'package:skills/features/availability/domain/pitch_settings_model.dart'
    as _i2;
import 'package:skills/features/booking/domain/booking_model.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePitchSettings_0 extends _i1.SmartFake implements _i2.PitchSettings {
  _FakePitchSettings_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AvailabilityService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAvailabilityService extends _i1.Mock
    implements _i3.AvailabilityService {
  MockAvailabilityService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.PitchSettings> getPitchSettings(int? pitchId) =>
      (super.noSuchMethod(
            Invocation.method(#getPitchSettings, [pitchId]),
            returnValue: _i4.Future<_i2.PitchSettings>.value(
              _FakePitchSettings_0(
                this,
                Invocation.method(#getPitchSettings, [pitchId]),
              ),
            ),
          )
          as _i4.Future<_i2.PitchSettings>);

  @override
  _i4.Future<List<_i2.PitchSettings>> getAllPitchSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getAllPitchSettings, []),
            returnValue: _i4.Future<List<_i2.PitchSettings>>.value(
              <_i2.PitchSettings>[],
            ),
          )
          as _i4.Future<List<_i2.PitchSettings>>);

  @override
  _i4.Future<List<_i5.Booking>> getBookingsForDate(
    DateTime? date,
    int? pitchId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingsForDate, [date, pitchId]),
            returnValue: _i4.Future<List<_i5.Booking>>.value(<_i5.Booking>[]),
          )
          as _i4.Future<List<_i5.Booking>>);
}
