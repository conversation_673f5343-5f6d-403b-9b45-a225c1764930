// Real-time availability provider implementing ADR-007 database-centric approach
// Replaces client-side slot calculations with database streaming

import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skills/core/utils/logger_service.dart';
import 'package:skills/features/auth/application/auth_service.dart';
import 'package:skills/features/availability/domain/time_slot_info_model.dart';
import 'package:skills/features/availability/application/database_slot_provider.dart';

part 'real_time_availability_provider.g.dart';

final logger = AppLogger();

/// Parameters for real-time availability provider
class RealTimeAvailabilityParams {
  final DateTime date;
  final int pitchId;

  const RealTimeAvailabilityParams({required this.date, required this.pitchId});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RealTimeAvailabilityParams &&
          runtimeType == other.runtimeType &&
          date == other.date &&
          pitchId == other.pitchId;

  @override
  int get hashCode => date.hashCode ^ pitchId.hashCode;
}

/// Database slot provider instance
@riverpod
DatabaseSlotProvider databaseSlotProvider(Ref ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return DatabaseSlotProvider(supabaseClient: supabase);
}

/// Real-time availability stream using database-centric approach (ADR-007)
@riverpod
Stream<List<TimeSlotInfo>> realTimeAvailability(
  Ref ref,
  RealTimeAvailabilityParams params,
) async* {
  try {
    logger.d(
      '🚀 ADR-007: Starting database-centric availability stream for pitch ${params.pitchId} on ${params.date}',
    );

    final provider = ref.read(databaseSlotProviderProvider);

    // Use database provider's real-time streaming capability
    await for (final slots in provider.watchSlots(
      params.pitchId,
      params.date,
    )) {
      logger.d('� Database stream update: ${slots.length} slots received');

      // Log slot status for debugging
      final availableCount = slots.where((s) => !s.isBooked).length;
      final bookedCount = slots.where((s) => s.isBooked).length;

      logger.d('� Available: $availableCount | 🔴 Booked: $bookedCount');

      yield slots;
    }
  } catch (e, stackTrace) {
    logger.e('❌ Error in database-centric availability stream', e, stackTrace);

    // Fallback to direct database fetch if streaming fails
    try {
      logger.w('🔄 Falling back to direct database fetch');
      final provider = ref.read(databaseSlotProviderProvider);
      final fallbackSlots = await provider.fetchSlots(
        params.pitchId,
        params.date,
      );
      yield fallbackSlots;
    } catch (fallbackError) {
      logger.e('❌ Fallback database fetch also failed', fallbackError);
      yield <TimeSlotInfo>[];
    }
  }
}

/// Optimistic booking state management
@riverpod
class OptimisticBookingState extends _$OptimisticBookingState {
  @override
  Map<String, bool> build() {
    return {}; // Maps slot key to optimistic booking state
  }

  void markSlotAsOptimisticallyBooked(DateTime startTime, DateTime endTime) {
    final slotKey =
        '${startTime.toIso8601String()}_${endTime.toIso8601String()}';
    state = {...state, slotKey: true};
  }

  void clearOptimisticBooking(DateTime startTime, DateTime endTime) {
    final slotKey =
        '${startTime.toIso8601String()}_${endTime.toIso8601String()}';
    final newState = Map<String, bool>.from(state);
    newState.remove(slotKey);
    state = newState;
  }

  void clearAllOptimisticBookings() {
    state = {};
  }
}

// End of real-time availability provider
// Client-side slot calculation removed - implementing database approach
