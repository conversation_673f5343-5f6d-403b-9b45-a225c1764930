import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skills/features/auth/application/auth_service.dart';
import 'package:skills/core/utils/logger_service.dart'; // Import the logger service

enum SignUpMode { password, otp }

class SignUpPage extends ConsumerStatefulWidget {
  const SignUpPage({super.key});

  @override
  ConsumerState<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends ConsumerState<SignUpPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  SignUpMode _signUpMode = SignUpMode.password; // Default to password sign-up
  final logger = AppLogger(); // Create a logger instance

  Future<void> _performSignUp() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        if (_signUpMode == SignUpMode.password) {
          await ref
              .read(authServiceProvider)
              .signUpWithEmailPassword(
                _emailController.text.trim(),
                _passwordController.text.trim(),
              );
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Sign-up successful! Please verify your email.'),
              ),
            );
            // Optionally navigate or clear fields
          }
        } else {
          // OTP Sign Up
          // Define your redirect URL
          const String emailRedirectTo =
              'io.supabase.skills://login-callback/'; // Or your chosen scheme

          await ref
              .read(authServiceProvider)
              .signInWithOtp(
                _emailController.text.trim(),
                emailRedirectTo, // Pass the redirect URL here
              );
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Magic link sent! Check your email.'),
              ),
            );
            // Optionally navigate or clear fields
          }
        }
      } catch (e, s) {
        // Added stack trace parameter 's'
        logger.e('Sign-up Exception: $e', s); // Corrected logger call

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Sign-up failed: ${e.toString()}')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _signUpMode == SignUpMode.password
              ? 'Sign Up with Password'
              : 'Sign Up with Magic Link',
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                // Email field (common for both modes)
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(labelText: 'Email'),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null ||
                        value.isEmpty ||
                        !value.contains('@')) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Password fields (only for password mode)
                if (_signUpMode == SignUpMode.password) ...[
                  TextFormField(
                    controller: _passwordController,
                    decoration: const InputDecoration(labelText: 'Password'),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a password';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _confirmPasswordController,
                    decoration: const InputDecoration(
                      labelText: 'Confirm Password',
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your password';
                      }
                      if (value != _passwordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                ],

                // OTP mode specific message or field (if any in future)
                if (_signUpMode == SignUpMode.otp) ...[
                  const SizedBox(
                    height: 16,
                  ), // Placeholder for any OTP specific UI
                  const Text(
                    "A magic link will be sent to your email address.",
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                ],

                _isLoading
                    ? const CircularProgressIndicator()
                    : ElevatedButton(
                      onPressed: _performSignUp,
                      child: Text(
                        _signUpMode == SignUpMode.password
                            ? 'Sign Up'
                            : 'Send Magic Link',
                      ),
                    ),
                const SizedBox(height: 12),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _signUpMode =
                          _signUpMode == SignUpMode.password
                              ? SignUpMode.otp
                              : SignUpMode.password;
                    });
                  },
                  child: Text(
                    _signUpMode == SignUpMode.password
                        ? 'Or, Sign Up with Magic Link'
                        : 'Or, Sign Up with Email & Password',
                  ),
                ),
                TextButton(
                  onPressed: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context); // Go back to Login Page
                    }
                  },
                  child: const Text('Already have an account? Login'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
