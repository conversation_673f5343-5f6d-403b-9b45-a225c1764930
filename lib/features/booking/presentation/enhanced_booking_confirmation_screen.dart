// Enhanced booking confirmation screen implementing ADR-006 optimistic booking patterns
// This screen uses the optimistic booking service for better UX and race condition handling

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skills/core/utils/date_formatters.dart';
import 'package:skills/features/availability/application/availability_service.dart';
import 'package:skills/features/booking/application/optimistic_booking_service.dart'
    as booking_service;

class EnhancedBookingConfirmationScreen extends ConsumerWidget {
  final int pitchId;
  final String pitchName;
  final DateTime selectedDate;
  final DateTime slotStartTime;
  final DateTime slotEndTime;

  const EnhancedBookingConfirmationScreen({
    super.key,
    required this.pitchId,
    required this.pitchName,
    required this.selectedDate,
    required this.slotStartTime,
    required this.slotEndTime,
  });

  static const routeName = 'enhanced-booking-confirmation';
  static const routePath = '/enhanced-booking-confirmation';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optimisticBookingState = ref.watch(
      booking_service.optimisticBookingServiceProvider,
    );
    final pitchSettingsAsync = ref.watch(pitchSettingsProvider(pitchId));

    // Listen to booking state changes for immediate feedback
    ref.listen<booking_service.OptimisticBookingState>(
      booking_service.optimisticBookingServiceProvider,
      (previous, current) {
        _handleBookingStateChange(context, ref, previous, current);
      },
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Confirm Booking'),
        leading:
            optimisticBookingState.isLoading
                ? null
                : IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => context.pop(),
                ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking Details Card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Booking Details',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    _buildDetailRow(
                      icon: Icons.sports_soccer,
                      label: 'Pitch',
                      value: pitchName,
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      icon: Icons.calendar_today,
                      label: 'Date',
                      value: AppDateFormats.dayMonthYear.format(selectedDate),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      icon: Icons.access_time,
                      label: 'Time',
                      value:
                          '${AppDateFormats.hourMinute.format(slotStartTime)} - ${AppDateFormats.hourMinute.format(slotEndTime)}',
                    ),
                    const SizedBox(height: 8),
                    pitchSettingsAsync.when(
                      data:
                          (settings) => _buildDetailRow(
                            icon: Icons.attach_money,
                            label: 'Price',
                            value:
                                'MWK ${settings.pricePerHour.toStringAsFixed(0)}',
                          ),
                      loading:
                          () => _buildDetailRow(
                            icon: Icons.attach_money,
                            label: 'Price',
                            value: 'Loading...',
                          ),
                      error:
                          (_, __) => _buildDetailRow(
                            icon: Icons.attach_money,
                            label: 'Price',
                            value: 'Error loading price',
                          ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Status indicator
            _buildStatusIndicator(optimisticBookingState),

            const Spacer(),

            // Action buttons
            _buildActionButtons(context, ref, optimisticBookingState),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),
        Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.normal),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusIndicator(
    booking_service.OptimisticBookingState bookingState,
  ) {
    if (bookingState.status == booking_service.OptimisticBookingStatus.idle) {
      return const SizedBox.shrink();
    }

    Color statusColor;
    IconData statusIcon;
    String statusMessage;

    switch (bookingState.status) {
      case booking_service.OptimisticBookingStatus.optimisticLoading:
        statusColor = Colors.blue;
        statusIcon = Icons.pending;
        statusMessage = bookingState.message ?? 'Preparing booking...';
        break;
      case booking_service.OptimisticBookingStatus.serverValidating:
        statusColor = Colors.orange;
        statusIcon = Icons.sync;
        statusMessage = bookingState.message ?? 'Validating with server...';
        break;
      case booking_service.OptimisticBookingStatus.success:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusMessage = bookingState.message ?? 'Booking confirmed!';
        break;
      case booking_service.OptimisticBookingStatus.conflict:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        statusMessage = bookingState.message ?? 'Booking conflict detected';
        break;
      case booking_service.OptimisticBookingStatus.error:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusMessage = bookingState.message ?? 'Booking failed';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Card(
      color: statusColor.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            if (bookingState.isLoading)
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                ),
              )
            else
              Icon(statusIcon, color: statusColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                statusMessage,
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    WidgetRef ref,
    booking_service.OptimisticBookingState bookingState,
  ) {
    return Column(
      children: [
        // Main action button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _getMainButtonAction(context, ref, bookingState),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getMainButtonColor(context, bookingState),
              foregroundColor: Colors.white,
            ),
            child: Text(_getMainButtonText(bookingState)),
          ),
        ),

        const SizedBox(height: 12),

        // Secondary action button (when applicable)
        if (_shouldShowSecondaryButton(bookingState))
          SizedBox(
            width: double.infinity,
            height: 48,
            child: OutlinedButton(
              onPressed: _getSecondaryButtonAction(context, ref, bookingState),
              child: Text(_getSecondaryButtonText(bookingState)),
            ),
          ),
      ],
    );
  }

  VoidCallback? _getMainButtonAction(
    BuildContext context,
    WidgetRef ref,
    booking_service.OptimisticBookingState bookingState,
  ) {
    switch (bookingState.status) {
      case booking_service.OptimisticBookingStatus.idle:
        return () => _confirmBooking(ref);
      case booking_service.OptimisticBookingStatus.optimisticLoading:
      case booking_service.OptimisticBookingStatus.serverValidating:
        return null; // Disabled during loading
      case booking_service.OptimisticBookingStatus.success:
        return () => _handleBookingSuccess(context, ref);
      case booking_service.OptimisticBookingStatus.conflict:
      case booking_service.OptimisticBookingStatus.error:
        return () => _retryBooking(ref);
    }
  }

  Color _getMainButtonColor(
    BuildContext context,
    booking_service.OptimisticBookingState bookingState,
  ) {
    switch (bookingState.status) {
      case booking_service.OptimisticBookingStatus.success:
        return Colors.green;
      case booking_service.OptimisticBookingStatus.conflict:
      case booking_service.OptimisticBookingStatus.error:
        return Colors.orange;
      default:
        return Theme.of(context).primaryColor;
    }
  }

  String _getMainButtonText(
    booking_service.OptimisticBookingState bookingState,
  ) {
    switch (bookingState.status) {
      case booking_service.OptimisticBookingStatus.idle:
        return 'Confirm Booking';
      case booking_service.OptimisticBookingStatus.optimisticLoading:
        return 'Booking...';
      case booking_service.OptimisticBookingStatus.serverValidating:
        return 'Validating...';
      case booking_service.OptimisticBookingStatus.success:
        return 'Done';
      case booking_service.OptimisticBookingStatus.conflict:
      case booking_service.OptimisticBookingStatus.error:
        return 'Try Again';
    }
  }

  bool _shouldShowSecondaryButton(
    booking_service.OptimisticBookingState bookingState,
  ) {
    return bookingState.status ==
            booking_service.OptimisticBookingStatus.success ||
        bookingState.status ==
            booking_service.OptimisticBookingStatus.conflict ||
        bookingState.status == booking_service.OptimisticBookingStatus.error;
  }

  VoidCallback? _getSecondaryButtonAction(
    BuildContext context,
    WidgetRef ref,
    booking_service.OptimisticBookingState bookingState,
  ) {
    switch (bookingState.status) {
      case booking_service.OptimisticBookingStatus.success:
        return () => _bookAnotherSlot(context, ref);
      case booking_service.OptimisticBookingStatus.conflict:
      case booking_service.OptimisticBookingStatus.error:
        return () => _cancelBooking(context, ref);
      default:
        return null;
    }
  }

  String _getSecondaryButtonText(
    booking_service.OptimisticBookingState bookingState,
  ) {
    switch (bookingState.status) {
      case booking_service.OptimisticBookingStatus.success:
        return 'Book Another Slot';
      case booking_service.OptimisticBookingStatus.conflict:
      case booking_service.OptimisticBookingStatus.error:
        return 'Cancel';
      default:
        return '';
    }
  }

  void _confirmBooking(WidgetRef ref) {
    ref
        .read(booking_service.optimisticBookingServiceProvider.notifier)
        .createBookingOptimistic(
          pitchId: pitchId,
          slotStartTime: slotStartTime,
          slotEndTime: slotEndTime,
        );
  }

  void _retryBooking(WidgetRef ref) {
    ref
        .read(booking_service.optimisticBookingServiceProvider.notifier)
        .resetState();
    // Optionally, automatically retry or let user click confirm again
    _confirmBooking(ref);
  }

  void _handleBookingSuccess(BuildContext context, WidgetRef ref) {
    ref
        .read(booking_service.optimisticBookingServiceProvider.notifier)
        .resetState();
    context.pop(); // Go back to availability screen
  }

  void _bookAnotherSlot(BuildContext context, WidgetRef ref) {
    ref
        .read(booking_service.optimisticBookingServiceProvider.notifier)
        .resetState();
    context.pop(); // Go back to availability screen
  }

  void _cancelBooking(BuildContext context, WidgetRef ref) {
    ref
        .read(booking_service.optimisticBookingServiceProvider.notifier)
        .cancelOptimisticBooking();
    context.pop();
  }

  void _handleBookingStateChange(
    BuildContext context,
    WidgetRef ref,
    booking_service.OptimisticBookingState? previous,
    booking_service.OptimisticBookingState current,
  ) {
    // Show snackbar for important state changes
    if (previous != null) {
      String? message;
      Color? backgroundColor;

      if (current.status == booking_service.OptimisticBookingStatus.success) {
        message = 'Booking confirmed successfully!';
        backgroundColor = Colors.green;
      } else if (current.status ==
          booking_service.OptimisticBookingStatus.conflict) {
        message = 'Booking conflict: ${current.message}';
        backgroundColor = Colors.orange;
      } else if (current.status ==
          booking_service.OptimisticBookingStatus.error) {
        message = 'Booking failed: ${current.message}';
        backgroundColor = Colors.red;
      }

      if (message != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
