// Optimistic booking service implementing race condition prevention from ADR-006
// This service handles booking creation with optimistic UI updates and rollback on conflicts

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:skills/core/utils/logger_service.dart';
import 'package:skills/features/auth/application/auth_service.dart';
import 'package:skills/features/booking/domain/booking_exceptions.dart';
import 'package:skills/features/booking/data/booking_repository.dart';
import 'package:skills/features/availability/application/availability_service.dart';
import 'package:skills/features/availability/application/real_time_availability_simple.dart';
import 'package:skills/features/availability/application/real_time_availability_provider.dart'
    as real_time;

part 'optimistic_booking_service.g.dart';

final logger = AppLogger();

/// Enhanced booking creation state that supports optimistic updates
enum OptimisticBookingStatus {
  idle,
  optimisticLoading, // Immediate optimistic update
  serverValidating, // Server-side validation in progress
  success,
  conflict, // Booking conflict detected
  error,
}

/// Enhanced booking creation state with detailed information
class OptimisticBookingState {
  final OptimisticBookingStatus status;
  final String? message;
  final Exception? exception;
  final DateTime? optimisticStartTime;
  final DateTime? optimisticEndTime;

  const OptimisticBookingState({
    required this.status,
    this.message,
    this.exception,
    this.optimisticStartTime,
    this.optimisticEndTime,
  });

  bool get isLoading =>
      status == OptimisticBookingStatus.optimisticLoading ||
      status == OptimisticBookingStatus.serverValidating;
  bool get isSuccess => status == OptimisticBookingStatus.success;
  bool get isConflict => status == OptimisticBookingStatus.conflict;
  bool get hasError =>
      status == OptimisticBookingStatus.error ||
      status == OptimisticBookingStatus.conflict;

  OptimisticBookingState copyWith({
    OptimisticBookingStatus? status,
    String? message,
    Exception? exception,
    DateTime? optimisticStartTime,
    DateTime? optimisticEndTime,
  }) {
    return OptimisticBookingState(
      status: status ?? this.status,
      message: message ?? this.message,
      exception: exception ?? this.exception,
      optimisticStartTime: optimisticStartTime ?? this.optimisticStartTime,
      optimisticEndTime: optimisticEndTime ?? this.optimisticEndTime,
    );
  }
}

/// Optimistic booking service with race condition handling
@riverpod
class OptimisticBookingService extends _$OptimisticBookingService {
  @override
  OptimisticBookingState build() {
    return const OptimisticBookingState(status: OptimisticBookingStatus.idle);
  }

  /// Create booking with optimistic UI updates and conflict handling
  Future<void> createBookingOptimistic({
    required int pitchId,
    required DateTime slotStartTime,
    required DateTime slotEndTime,
  }) async {
    try {
      logger.i(
        'Starting optimistic booking: pitch=$pitchId, slot=${slotStartTime.toIso8601String()}',
      );

      // 1. Immediate optimistic update
      state = OptimisticBookingState(
        status: OptimisticBookingStatus.optimisticLoading,
        message: 'Booking in progress...',
        optimisticStartTime: slotStartTime,
        optimisticEndTime: slotEndTime,
      );

      // 2. Update optimistic state in UI
      ref
          .read(optimisticBookingStateProvider.notifier)
          .markSlotAsOptimisticallyBooked(slotStartTime, slotEndTime);

      // 3. Short delay to show optimistic state (optional, for UX)
      await Future.delayed(const Duration(milliseconds: 100));

      // 4. Server validation phase
      state = state.copyWith(
        status: OptimisticBookingStatus.serverValidating,
        message: 'Validating booking...',
      );

      // 5. Try atomic booking creation via Edge Function
      final success = await _createBookingAtomic(
        pitchId: pitchId,
        slotStartTime: slotStartTime,
        slotEndTime: slotEndTime,
      );

      if (success) {
        // 6. Success - booking confirmed
        state = OptimisticBookingState(
          status: OptimisticBookingStatus.success,
          message: 'Booking confirmed!',
          optimisticStartTime: slotStartTime,
          optimisticEndTime: slotEndTime,
        );

        // 7. Clear optimistic state (real data will come via real-time update)
        ref
            .read(optimisticBookingStateProvider.notifier)
            .clearOptimisticBooking(slotStartTime, slotEndTime);

        // 8. Invalidate related providers to refresh data
        _invalidateRelatedProviders(pitchId, slotStartTime);

        logger.i('Booking created successfully');
      }
    } on SlotUnavailableException catch (e) {
      // Conflict detected - slot was booked by another user
      await _handleBookingConflict(e, slotStartTime, slotEndTime);
    } on BookingLimitReachedException catch (e) {
      // User reached booking limit
      await _handleBookingLimitError(e, slotStartTime, slotEndTime);
    } catch (e, stackTrace) {
      // General error
      await _handleGeneralError(e, stackTrace, slotStartTime, slotEndTime);
    }
  }

  /// Atomic booking creation using Edge Function (fallback to repository)
  Future<bool> _createBookingAtomic({
    required int pitchId,
    required DateTime slotStartTime,
    required DateTime slotEndTime,
  }) async {
    final supabase = ref.read(supabaseClientProvider);

    try {
      // Get the current user
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Try Edge Function first (if implemented)
      final response = await supabase.functions.invoke(
        'create_booking_atomic',
        body: {
          'pitch_id': pitchId,
          'slot_start_time': slotStartTime.toIso8601String(),
          'slot_end_time': slotEndTime.toIso8601String(),
          'user_id': user.id, // Add the missing user_id parameter
        },
      );

      if (response.status == 200 || response.status == 201) {
        logger.d('Booking created via Edge Function');
        return true;
      } else if (response.status == 409) {
        // Conflict detected
        final errorData = response.data;
        throw SlotUnavailableException(
          errorData['message'] ?? 'This slot was just booked by another user',
        );
      } else {
        throw Exception('Edge Function error: ${response.status}');
      }
    } catch (e) {
      if (e is SlotUnavailableException) {
        rethrow;
      }

      // Before falling back to repository, check if the booking was actually created
      // This prevents double booking when edge function succeeds but client fails to parse response
      logger.d(
        'Edge Function failed, checking if booking was actually created: $e',
      );

      final supabase = ref.read(supabaseClientProvider);
      final currentUser = supabase.auth.currentUser;
      if (currentUser == null) {
        throw const UserNotAuthenticatedException(
          'You must be logged in to make a booking',
        );
      }

      // Check if a booking already exists for this user, pitch, and time slot
      final bookingRepository = ref.read(bookingRepositoryProvider);
      final existingBooking = await _checkForExistingBooking(
        bookingRepository,
        currentUser.id,
        pitchId,
        slotStartTime,
        slotEndTime,
      );

      if (existingBooking != null) {
        logger.i('Booking was already created successfully by edge function');
        return true;
      }

      // If no existing booking found, fallback to repository method
      logger.d('No existing booking found, falling back to repository');
      await bookingRepository.createBooking(
        pitchId: pitchId,
        slotStartTime: slotStartTime,
        slotEndTime: slotEndTime,
      );

      return true;
    }
  }

  /// Handle booking conflict (slot unavailable)
  Future<void> _handleBookingConflict(
    SlotUnavailableException e,
    DateTime slotStartTime,
    DateTime slotEndTime,
  ) async {
    logger.w('Booking conflict detected: ${e.message}');

    state = OptimisticBookingState(
      status: OptimisticBookingStatus.conflict,
      message: e.message,
      exception: e,
      optimisticStartTime: slotStartTime,
      optimisticEndTime: slotEndTime,
    );

    // Clear optimistic booking immediately
    ref
        .read(optimisticBookingStateProvider.notifier)
        .clearOptimisticBooking(slotStartTime, slotEndTime);

    // Force refresh availability to show current state
    _invalidateRelatedProviders(null, slotStartTime);
  }

  /// Handle booking limit error
  Future<void> _handleBookingLimitError(
    BookingLimitReachedException e,
    DateTime slotStartTime,
    DateTime slotEndTime,
  ) async {
    logger.w('Booking limit reached: ${e.message}');

    state = OptimisticBookingState(
      status: OptimisticBookingStatus.error,
      message: e.message,
      exception: e,
      optimisticStartTime: slotStartTime,
      optimisticEndTime: slotEndTime,
    );

    // Clear optimistic booking
    ref
        .read(optimisticBookingStateProvider.notifier)
        .clearOptimisticBooking(slotStartTime, slotEndTime);
  }

  /// Handle general errors
  Future<void> _handleGeneralError(
    dynamic error,
    StackTrace stackTrace,
    DateTime slotStartTime,
    DateTime slotEndTime,
  ) async {
    logger.e('Booking creation failed with general error', error, stackTrace);

    state = OptimisticBookingState(
      status: OptimisticBookingStatus.error,
      message: 'Failed to create booking. Please try again.',
      exception: error is Exception ? error : Exception(error.toString()),
      optimisticStartTime: slotStartTime,
      optimisticEndTime: slotEndTime,
    );

    // Clear optimistic booking
    ref
        .read(optimisticBookingStateProvider.notifier)
        .clearOptimisticBooking(slotStartTime, slotEndTime);
  }

  /// Invalidate related providers to refresh data
  void _invalidateRelatedProviders(int? pitchId, DateTime slotStartTime) {
    // Invalidate user bookings
    ref.invalidate(userBookingsProvider);

    // Invalidate availability for the specific date
    if (pitchId != null) {
      final date = DateTime(
        slotStartTime.year,
        slotStartTime.month,
        slotStartTime.day,
      );

      // Invalidate both static and real-time availability providers
      ref.invalidate(availableSlotsProvider((date: date, pitchId: pitchId)));

      // Force refresh real-time availability to ensure immediate update
      ref.invalidate(
        real_time.realTimeAvailabilityProvider(
          real_time.RealTimeAvailabilityParams(date: date, pitchId: pitchId),
        ),
      );

      logger.d(
        'Invalidated providers for date $date, pitch $pitchId after booking',
      );
    } else {
      logger.d('Could not invalidate availability providers - pitchId is null');
    }
  }

  /// Reset booking state (call after handling success/error)
  void resetState() {
    state = const OptimisticBookingState(status: OptimisticBookingStatus.idle);
  }

  /// Cancel optimistic booking (for user cancellation)
  void cancelOptimisticBooking() {
    if (state.optimisticStartTime != null && state.optimisticEndTime != null) {
      ref
          .read(optimisticBookingStateProvider.notifier)
          .clearOptimisticBooking(
            state.optimisticStartTime!,
            state.optimisticEndTime!,
          );
    }

    state = const OptimisticBookingState(status: OptimisticBookingStatus.idle);
  }

  /// Helper method to check if a booking already exists for the given parameters
  Future<Map<String, dynamic>?> _checkForExistingBooking(
    BookingRepository repository,
    String userId,
    int pitchId,
    DateTime slotStartTime,
    DateTime slotEndTime,
  ) async {
    try {
      final supabase = ref.read(supabaseClientProvider);

      // Check for existing booking with exact match on time and user
      final response =
          await supabase
              .from('bookings')
              .select('id, slot_start_time, slot_end_time, status')
              .eq('user_id', userId)
              .eq('pitch_id', pitchId)
              .eq('slot_start_time', slotStartTime.toIso8601String())
              .eq('slot_end_time', slotEndTime.toIso8601String())
              .eq('status', 'confirmed')
              .maybeSingle();

      return response;
    } catch (e) {
      logger.e('Error checking for existing booking: $e');
      return null;
    }
  }
}
