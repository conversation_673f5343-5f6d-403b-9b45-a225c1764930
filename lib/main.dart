import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart'; // Import google_fonts
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:app_links/app_links.dart';
import 'dart:async';
import 'package:skills/routing/app_router.dart'; // Import the new app router
import 'package:skills/core/utils/logger_service.dart'; // Import the logger service

AppLinks? _appLinks;
final logger = AppLogger(); // Create a logger instance

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");

  final supabaseUrl = dotenv.env['SUPABASE_URL'];
  final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'];
  logger.d('DEBUG: SUPABASE_URL from .env: $supabaseUrl');
  logger.d('DEBUG: SUPABASE_ANON_KEY from .env: $supabaseAnonKey');

  if (supabaseUrl == null || supabaseAnonKey == null) {
    logger.e(
      'ERROR: SUPABASE_URL or SUPABASE_ANON_KEY is null. Check your .env file.',
    );
    return;
  }

  await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);

  _appLinks = AppLinks();

  _appLinks?.uriLinkStream.listen((uri) {
    logger.i('Received deep link while app is running: $uri');
    // Potentially use goRouterProvider to navigate based on the URI if needed
    // For Supabase auth, it often handles session refresh automatically.
  });

  try {
    final initialUri = await _appLinks?.getInitialLink();
    if (initialUri != null) {
      logger.i('App opened with initial deep link: $initialUri');
      // Supabase SDK handles auth. GoRouter will react to auth state changes.
    }
  } catch (e) {
    logger.e('Error getting initial deep link: $e');
  }

  runApp(const ProviderScope(child: MyApp()));
}

final supabase = Supabase.instance.client;

class MyApp extends ConsumerWidget {
  // Changed to ConsumerWidget
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Added WidgetRef
    final router = ref.watch(goRouterProvider); // Watch the router provider

    // Define a base text theme using GoogleFonts
    final baseTextTheme = GoogleFonts.latoTextTheme(
      Theme.of(context).textTheme,
    );
    // Define a primary text theme for headlines, etc.
    final primaryTextTheme = GoogleFonts.montserratTextTheme(
      Theme.of(context).primaryTextTheme,
    );

    return MaterialApp.router(
      title: 'Skills Pitch Booking',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.teal),
        useMaterial3: true,
        textTheme: baseTextTheme.copyWith(
          displayLarge: primaryTextTheme.displayLarge,
          displayMedium: primaryTextTheme.displayMedium,
          displaySmall: primaryTextTheme.displaySmall,
          headlineLarge: primaryTextTheme.headlineLarge,
          headlineMedium: primaryTextTheme.headlineMedium,
          headlineSmall: primaryTextTheme.headlineSmall,
          titleLarge: primaryTextTheme.titleLarge,
          titleMedium: primaryTextTheme.titleMedium,
          titleSmall: primaryTextTheme.titleSmall,
        ),
        // You can also define a darkTheme similarly if needed
        // darkTheme: ThemeData.dark().copyWith(
        //   colorScheme: ColorScheme.fromSeed(seedColor: Colors.teal, brightness: Brightness.dark),
        //   useMaterial3: true,
        //   textTheme: GoogleFonts.latoTextTheme(ThemeData.dark().textTheme),
        // ),
      ),
      routerConfig: router, // Use routerConfig
    );
  }
}
