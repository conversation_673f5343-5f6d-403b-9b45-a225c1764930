<!-- 
PROJECT LOGO 
<br />
<div align="center">
  <a href="https://github.com/your_username/skills">
    <img src="images/logo.png" alt="Logo" width="80" height="80">
  </a>
-->

<h3 align="center">Skills - Football Pitch Booking App</h3>

  <p align="center">
    A Flutter application for booking local football pitches, built with Supabase and Riverpod
    <br />
    <a href="docs/"><strong>Explore the docs »</strong></a>
    <br />
    <br />
    <!-- <a href="https://github.com/your_username/skills">View Demo</a>
    · -->
    <a href="https://github.com/your_username/skills/issues">Report Bug</a>
    ·
    <a href="https://github.com/your_username/skills/issues">Request Feature</a>
  </p>
</div>

<!-- TABLE OF CONTENTS -->
<details>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#about-the-project">About The Project</a>
      <ul>
        <li><a href="#built-with">Built With</a></li>
      </ul>
    </li>
    <li>
      <a href="#getting-started">Getting Started</a>
      <ul>
        <li><a href="#prerequisites">Prerequisites</a></li>
        <li><a href="#installation">Installation</a></li>
      </ul>
    </li>
    <li><a href="#current-features">Current Features</a></li>
    <li><a href="#roadmap">Roadmap</a></li>
    <li><a href="#development-notes">Development Notes</a></li>
    <li><a href="#known-issues">Known Issues</a></li>
    <li><a href="#contributing">Contributing</a></li>
    <li><a href="#license">License</a></li>
    <li><a href="#contact">Contact</a></li>
  </ol>
</details>

## About The Project

<!-- [Product Name Screen Shot][product-screenshot] -->

This Flutter application enables users to book time slots at local football pitches. The app provides a complete booking system with user authentication, real-time availability checking, and booking management.

**Key Features:**
* User authentication (Email/Password and Magic Link/OTP)
* Multi-pitch availability viewing
* Real-time slot booking with conflict prevention
* Personal booking management dashboard
* Dynamic pricing with peak hour calculations
* Booking limits and user-friendly feedback

The application follows clean architecture principles with Riverpod for state management and Supabase as the backend service.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Built With

This project is built using modern cross-platform development tools and services:

* [![Flutter][Flutter.dev]][Flutter-url]
* [![Dart][Dart.dev]][Dart-url]
* [![Supabase][Supabase.com]][Supabase-url]
* [![Riverpod][Riverpod.dev]][Riverpod-url]

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Getting Started

To get a local copy up and running, follow these simple steps.

### Prerequisites

* Flutter SDK (3.0.0 or higher)
  ```sh
  flutter --version
  ```
* Dart SDK (included with Flutter)
* A Supabase account and project

### Installation

1. Clone the repository
   ```sh
   git clone https://github.com/your_username/skills.git
   ```
2. Navigate to the project directory
   ```sh
   cd skills
   ```
3. Install Flutter dependencies
   ```sh
   flutter pub get
   ```
4. Set up Supabase configuration
   ```sh
   # Create lib/core/config/supabase_config.dart with your Supabase credentials
   # (See docs/development_guidelines.md for details)
   ```
5. Run the application
   ```sh
   flutter run
   ```

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Current Features

### ✅ Completed Features

*   **Authentication System:**
    *   Email/Password sign-up and login
    *   Magic Link (OTP) authentication with deep linking
    *   Secure logout functionality

*   **Pitch Management:**
    *   Multi-pitch selection interface
    *   Dynamic availability display based on selected pitch
    *   Automatic next-day suggestion when current day is fully booked

*   **Booking System:**
    *   Real-time slot booking with conflict prevention
    *   Maximum 4 active bookings per user with enforcement
    *   Enhanced booking confirmation flow
    *   Dynamic pricing with peak hour calculations
    *   Booking history management with tabbed interface

*   **Technical Foundation:**
    *   Clean architecture with feature-based structure
    *   Comprehensive logging system
    *   Material 3 UI with custom theming
    *   Extensive test coverage (unit and widget tests)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Roadmap

### Phase 1: Enhanced Settings & Configuration (Current)
- [ ] **Dynamic Pitch Settings Implementation**
  - [ ] Replace hardcoded booking limits with database-driven configuration
  - [ ] Apply database migration for pricing and limits fields
  - [ ] Implement real-time settings updates

### Phase 2: Payment Integration
- [ ] **Local Payment Methods**
  - [ ] Research and integrate appropriate payment gateway
  - [ ] Implement secure payment processing
  - [ ] Add payment confirmation and receipt system

### Phase 3: Admin Interface
- [ ] **Pitch Owner Dashboard**
  - [ ] Admin authentication and role management
  - [ ] Booking management interface
  - [ ] Dynamic pricing and availability configuration
  - [ ] Analytics and reporting features

### Phase 4: Advanced Features
- [ ] **Multi-booking Cart System** (if user demand exists)
- [ ] **Mobile-specific Enhancements**
- [ ] **Notification System**
- [ ] **Advanced Analytics**

See the [Development Notes](docs/dev_notes/) for detailed implementation plans and decision logs.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Development Notes

This project maintains detailed development notes and architectural decision records (ADRs):

* **[Development Notes](docs/dev_notes/)** - Chronological development sessions and implementation details
* **[ADRs](docs/adrs/)** - Architectural decision records
* **[Development Guidelines](docs/development_guidelines.md)** - Coding standards and best practices
* **[Database Migrations](docs/database_migrations/)** - Database schema changes and migration scripts

### Current Development Session
**Focus**: Implementation review, database migration, and dynamic settings completion  
**Status**: 🔄 ACTIVE  
**File**: [`docs/dev_notes/dev_notes-current.md`](docs/dev_notes/dev_notes-current.md)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

*   **Authentication (Wrap-up):**
    *   [x] **Configure Deep Linking for OTP:**
        *   [x] Configure `io.supabase.skills://login-callback/` in Supabase Dashboard URL Configuration.
        *   [x] Implement Android deep linking (`AndroidManifest.xml`).
        *   [x] Implement iOS deep linking (`Info.plist`).
        *   [x] Handle deep link in Flutter app (e.g., `uni_links` or `app_links` package, update `AuthGate` or `main.dart`).
    *   [x] Thoroughly test Email/Password sign-up & login flows.
    *   [x] Thoroughly test OTP/Magic Link sign-up & login flows (including link expiry and invalid link scenarios).
*   **Supabase Schema (Iterations 1 & 2):**
    *   [x] Define `profiles` table.
    *   [x] Define `pitch_settings` table (id (SERIAL PK), `pitch_name` (TEXT NOT NULL), open_time (TIME NOT NULL), close_time (TIME NOT NULL), slot_duration_minutes (INTEGER), cancellation_window_hours (INTEGER)).
    *   [x] Define `bookings` table (id (SERIAL PK), user_id (UUID, FK to auth.users), `pitch_id` (INTEGER, FK to `pitch_settings`), slot_start_time (TIMESTAMPTZ), slot_end_time (TIMESTAMPTZ), status (TEXT), created_at (TIMESTAMPTZ), `total_price`, `updated_at`).
        *   [x] Constraint on `status`: `CHECK (status IN (\'pending\', \'confirmed\', \'cancelled\'))`.
    *   [x] Created `additional_items` table.
    *   [x] Created `booking_items` table.
    *   [x] Set up initial RLS policies for these tables.
    *   [x] Manually populate `pitch_settings` in Supabase for initial testing.
    *   [x] Added `updated_at` trigger for `bookings`.
*   **Viewing Availability & Pitch Selection with Riverpod:**
    *   [x] Create `AvailabilityRepository/Service` class.
    *   [x] Create Riverpod providers for fetching `pitch_settings` (family by `pitchId`), all `pitch_settings` (`allPitchesProvider`), and `bookings` (family by `date` and `pitchId`).
    *   [x] Create `selectedPitchIdProvider` (StateProvider) to manage the active pitch.
    *   [x] Create `AvailabilityScreen` UI.
    *   [x] Implement UI to select a date (calendar widget/date picker).
    *   [x] Implement UI to select a pitch (DropdownButtonFormField).
    *   [x] Implement logic (Riverpod providers/notifiers) to:
        *   [x] Fetch all `pitch_settings` for selection.
        *   [x] Fetch specific `pitch_settings` for the selected pitch.
        *   [x] Generate potential time slots for a selected day and pitch.
        *   [x] Fetch existing `bookings` for the selected day and pitch.
        *   [x] Determine available slots.
    *   [x] Display available slots dynamically, reacting to state changes from Riverpod.
    *   [x] **Enhanced `AvailabilityScreen` to show next available day if today is full/past closing.**
    *   [ ] Ensure UI is responsive for mobile and web.
*   **Booking Management & Display (Iteration 2 Features):**
    *   [x] `Booking` domain model created.
    *   [x] `BookingRepository` for `createBooking` and `fetchUserBookings`.
    *   [x] `BookingCreationNotifier` and `userBookingsProvider`.
    *   [x] Booking creation integrated into `AvailabilityScreen`.
    *   [x] "My Bookings" screen implemented **with tabbed view for upcoming/past bookings and enhanced styling.**
    *   [x] Navigation to "My Bookings" screen.
    *   [x] `TimeSlotInfo` model and `availableSlotsProvider` updated to **filter out booked slots.**
    *   [x] `AvailabilityScreen` UI updated to **remove booked slots from the list and refine styling for unavailable past slots.**
    *   [x] **Booking limit (max 4) enforced with SnackBar notification and navigation to "My Bookings".**
*   **Testing (TDD Approach for new features):**
    *   [x] Set up Flutter testing environment for Riverpod providers.
    *   [x] Write unit tests for business logic (slot generation, availability calculation, pitch selection interactions).
        *   [x] Happy Path - Basic availability (no bookings)
        *   [x] Happy Path - Simple booking (one booking, slots available around it)
        *   [x] Booking at the start of the day
        *   [x] Booking at the end of the day
        *   [x] Booking spanning multiple slots
        *   [x] Multiple bookings, no slots available (fully booked).
        *   [x] Edge cases: Pitch open time equals/after close time, slot duration longer than open window.
        *   [x] Error cases: `getPitchSettings` fails, `getBookingsForDate` fails, `allPitchesProvider` fails.
    *   [x] Write widget tests for UI components (`AvailabilityScreen`, `MyBookingsScreen`).
        *   [x] Test loading states (pitches, settings, slots).
        *   [x] Test data display (selected date, pitch settings, list of available/booked slots, pitch selection dropdown).
        *   [x] Test "no available slots" / "no pitches" messages.
        *   [x] Test date picker does not allow selecting and confirming past dates.
        *   [x] Test error states for all relevant providers.
        *   [x] Test booking creation flow (success/error feedback).
        *   [x] (Optional) Test date picker interaction (selecting a new date re-fetches slots).
        *   [x] All `AvailabilityScreen` widget tests now pass consistently with proper Supabase mock setup.
        *   [x] Widget tests for `MyBookingsScreen` (loading, data, error states, **tab navigation**).

### Pre-Iteration 3: Enhanced Booking Flow with Confirmation ✅ COMPLETED

Enhanced booking process implemented successfully:

*   [x] **Modify `AvailabilityScreen`**:
    *   [x] Instead of direct booking, tapping "Book" on a time slot navigates to `BookingConfirmationScreen`.
    *   [x] Slot details (pitch, time) are passed to the confirmation screen via route parameters.
*   [x] **Create `BookingConfirmationScreen` (New Widget)**:
    *   [x] Displays summary of the booking (pitch, date, time, "Football included").
    *   [x] **Actions**:
        *   [x] **"Confirm & Proceed to Book" Button**:
            *   [x] Invokes the existing `createBooking` logic.
            *   [x] Handles loading, success (navigate back to Availability, refresh data), and error states.
        *   [x] **"Cancel" Button**: Navigates back to `AvailabilityScreen`.
*   [x] **Route Integration**:
    *   [x] Added route configuration for `BookingConfirmationScreen` with query parameters.
    *   [x] Navigation integration from `SlotListItem` in `AvailabilityScreen`.
*   [x] **Testing**:
    *   [x] Widget tests for `BookingConfirmationScreen`:
        *   [x] Test display of booking summary (pitch, date, time).
        *   [x] Test "Confirm & Proceed to Book" button:
            *   [x] Mock `createBooking` success: verify navigation and data refresh.
            *   [x] Mock `createBooking` failure: verify error message display.
            *   [x] Test loading state display.
        *   [x] Test "Cancel" button: verify navigation to previous screen.
*   **Future Payment Integration**:
    *   This `BookingConfirmationScreen` will be the place to integrate the "Proceed to Payment" step in Iteration 5.

### Remaining Testing Tasks

    *   [ ] **`AvailabilityScreen` - Booking Limit Feature:**
        *   [ ] Widget Test: Verify SnackBar with "View Bookings" action appears when user with 4 bookings attempts to select a slot.
        *   [ ] Widget Test: Verify booking is prevented if limit is reached (e.g., no navigation to confirmation).
        *   [ ] (Optional) Unit Test: For booking limit check logic if complex and separable.
    *   [ ] **`AvailabilityScreen` - Auto-advancing Date Feature:**
        *   [ ] Unit Test: For logic determining if the date should auto-advance.
        *   [ ] Widget Test: Verify screen displays next available day if today has no slots.
        *   [ ] Widget Test: Verify screen displays next available day if current time is past today's closing time.
        *   [ ] Widget Test: Verify screen displays current day if slots are available and not past closing time.

### ⚠️ MAJOR ARCHITECTURAL DECISION: Dynamic Settings & Multi-Booking Cart System

**Status**: PLANNING PHASE - See [ADR-005](docs/adrs/ADR-005-dynamic-settings-and-cart-system.md) for detailed design

**Context**: The current single booking flow with hardcoded settings doesn't scale for production requirements:
- Pricing support needed (currently free)
- Users book multiple slots per session
- Admin configuration required
- Shopping cart UX expected for multi-booking

**Proposed Solution**: Phased implementation of dynamic pitch settings and shopping cart system

#### **Phase 1: Dynamic Pitch Settings Foundation** (Current Priority)
*   [ ] **Database Schema Enhancement**:
    *   [ ] Create comprehensive `pitch_settings` table with pricing, slot duration, booking limits
    *   [ ] Add RLS policies for user read access and admin management
    *   [ ] Migration scripts for existing data
*   [ ] **PitchSettings Domain Model**:
    *   [ ] `PitchSettings` model with comprehensive configuration
    *   [ ] Price calculation methods and slot generation
    *   [ ] Validation and business rules
*   [ ] **Data Layer Enhancement**:
    *   [ ] `PitchSettingsRepository` with CRUD operations
    *   [ ] Real-time settings updates support
    *   [ ] Caching strategy for performance
*   [ ] **State Management Updates**:
    *   [ ] Riverpod providers for settings management
    *   [ ] Replace hardcoded values throughout codebase
    *   [ ] Backward compatibility during transition

#### **Phase 2: Multi-Booking Cart System** (Next Sprint)
*   [ ] **Shopping Cart Models**:
    *   [ ] `BookingItem` model for cart items
    *   [ ] Cart state management with Riverpod
    *   [ ] Conflict detection and validation
*   [ ] **Enhanced UI Flow**:
    *   [ ] Transform `BookingConfirmationScreen` into `BookingCartScreen`
    *   [ ] "Add to Cart" functionality in `AvailabilityScreen`
    *   [ ] Cart review with pricing breakdown
    *   [ ] Batch booking confirmation
*   [ ] **Business Logic Enhancement**:
    *   [ ] Dynamic booking limit enforcement per pitch
    *   [ ] Cart-level conflict detection
    *   [ ] Price calculations and totals

#### **Phase 3: Integration & Production Readiness** (Following Sprint)
*   [ ] **Real-time Updates**: Admin setting changes propagate immediately
*   [ ] **Payment Integration Points**: Prepare cart for payment flow
*   [ ] **Performance Optimization**: Caching and query optimization
*   [ ] **Comprehensive Testing**: End-to-end cart and booking flows

**Risk Mitigation**: 
- Phased implementation maintains system stability
- Backward compatibility preserved during transition
- Each phase delivers incremental value

---

### Iteration 3: Cancellation & Basic Add-ons (DEFERRED - After Cart System)

*   [ ] UI for cancellation confirmation.
*   [ ] Extend `BookingRepository/Service` and Riverpod providers for cancelling bookings (respecting `cancellation_window_hours`).
*   [ ] Supabase schema for `additional_items` (name, description, price) and `booking_items` (linking bookings to items) - *Initial tables exist, may need refinement*.
*   [ ] UI to display and select add-ons during booking flow.
*   [ ] Logic to save selected add-ons with the booking.
*   [ ] Basic email notification for cancellation.

### Iteration 4: Admin Panel MVP (View Bookings, Block Slots)

*   [ ] Admin login mechanism (role-based access).
*   [ ] Differentiate admin users in Supabase (custom claim or separate table), update RLS.
*   [ ] Admin UI to view all bookings.
*   [ ] Admin UI to create "blocker" entries (e.g., for maintenance).
*   [ ] Update availability logic to respect admin-blocked slots.

### Iteration 5: Payment Gateway Integration

*   [ ] Research and select Flutter packages/SDKs for Airtel Money, TNM Mpamba, and local bank payments.
*   [ ] UI for payment method selection.
*   [ ] Securely handle payment processing (likely via Supabase Edge Functions).
*   [ ] Update booking status upon successful payment.
*   [ ] Handle payment failures/pending states.

### Future Iterations (Beyond MVP)

*   SMS Authentication & Notifications.
*   Advanced Admin Features (Pricing Management, User Management, Reporting).
*   User Profile Management.
*   Push Notifications.
*   UI Polish & Animations.

## Getting Started (Original Flutter Template)

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Known Issues

This section documents current issues in the codebase, prioritized by severity and impact on functionality. Each issue includes a plan for resolution.

### 🔴 Critical Issues (Must Fix Immediately)

#### 1. Duplicate `userBookingsProvider` Declaration (Build Error)
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/features/booking/application/booking_service.dart:65` (Fixed)
- `lib/features/booking/data/booking_repository.dart:16` (Provider kept here)
- `lib/features/booking/presentation/booking_confirmation_screen.dart:61` (Import updated)

**Problem:** The `userBookingsProvider` was defined in both the application service and data repository layers, causing ambiguous import errors.

**Resolution:** ✅ Removed duplicate provider from `booking_service.dart` and updated all imports to use the repository version.

---

#### 2. Missing Route Constants in `MyBookingsScreen`
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/routing/app_router.dart:104` (Fixed)
- `test/features/booking/presentation/booking_confirmation_screen_test.dart:144-145` (Fixed)

**Problem:** `MyBookingsScreen` was missing `routeName` and `routePath` static constants referenced in routing and tests.

**Resolution:** ✅ Added missing static constants to `MyBookingsScreen`.

---

#### 3. Missing Supabase Mock Setup in Widget Tests
**Status:** ✅ RESOLVED  
**Files Affected:**
- `test/features/availability/presentation/availability_screen_test.dart` (Fixed)

**Problem:** Widget tests for `AvailabilityScreen` were failing with "SupabaseNotInitializedException" because date picker interactions triggered data fetching without proper Supabase mock setup.

**Resolution:** ✅ Added proper test infrastructure including:
- `setUpAll()` and `tearDownAll()` methods with Supabase mock initialization
- Added `...testProviderOverrides` to include base Supabase mock overrides
- Added missing `userBookingsProvider` override to prevent authentication errors
- Fixed provider type safety with `<Booking>[]` instead of `<dynamic>[]`
- All 6 `AvailabilityScreen` widget tests now pass consistently

---

#### 4. Booking Model JsonKey Annotation Issues
**Status:** 🔄 PARTIALLY RESOLVED (Temporary Fix)  
**Files Affected:**
- `lib/features/booking/domain/booking_model.dart`

**Problem:** JsonKey annotations on Freezed factory constructor parameters causing build failures and preventing code generation.

**Temporary Resolution:** 🔧 Implemented manual JSON parsing in fromJson method, disabled JsonKey annotations and .g.dart generation.

**Permanent Fix Required:** 
1. Research proper JsonKey usage with Freezed factory constructors
2. Re-enable JsonSerializable code generation
3. Replace manual JSON parsing with generated methods

---

### 🟡 High Priority Issues (Fix Before Next Release)

#### 3. Inconsistent Booking Model Structure 
**Status:** Recently Fixed (Generated files updated)  
**Files Affected:**
- `lib/features/booking/domain/booking_model.dart`

**Problem:** JsonKey annotations incorrectly placed on factory constructor parameters instead of fields.

**Impact:** JSON serialization/deserialization errors, potential runtime crashes.

**Resolution Plan:**
1. ✅ Fixed by running build_runner
2. Monitor for any remaining serialization issues
3. Add unit tests for JSON serialization

---

#### 4. Deprecated API Usage
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/routing/app_router.dart:35` (Fixed - replaced AsyncValue.stream with .future.asStream())
- `lib/features/availability/presentation/widgets/slot_list_item.dart:84,112-114` (Fixed - replaced MaterialState with WidgetState, withOpacity with withValues)

**Problem:** Using deprecated Flutter/Riverpod APIs that will be removed in future versions.

**Resolution:** ✅ Replaced all deprecated API usage with modern equivalents.

---

#### 5. Print Statement Usage (Code Quality)
**Status:** ✅ RESOLVED  
**Files Affected:**
- `lib/features/booking/presentation/booking_confirmation_screen.dart:159` (Fixed)

**Problem:** Using print() statements instead of proper logging framework.

**Resolution:** ✅ Replaced print with debugPrint and added TODO for logging service migration.

---

### 🟢 Medium Priority Issues (Fix During Next Iteration)

#### 6. Code Quality and Linting Issues
**Status:** Active Warnings  
**Files Affected:**
- `lib/features/availability/presentation/availability_screen.dart:362`
- `lib/features/availability/presentation/widgets/availability_details.dart:95`
- `lib/features/booking/data/booking_repository.dart:119`
- `lib/features/booking/presentation/my_bookings_screen.dart:4`

**Problem:** Various linting violations including missing braces, unnecessary type checks, unused imports.

**Impact:** Code maintainability, potential bugs, inconsistent style.

**Resolution Plan:**
1. Add braces to single-line if statements
2. Remove unnecessary string interpolation braces
3. Remove unused imports and unnecessary type checks
4. Run `flutter analyze` and fix all remaining issues

---

#### 7. BuildContext Async Usage Warning
**Status:** Active Warning  
**Files Affected:**
- `lib/features/home/<USER>/home_page.dart:25`

**Problem:** Using BuildContext across async gaps without mounted checks.

**Impact:** Potential runtime errors if widget is disposed during async operations.

**Resolution Plan:**
1. Add `mounted` checks before using BuildContext after async operations
2. Review all async functions that use BuildContext
3. Implement proper error handling patterns

---

### 🔵 Low Priority Issues (Technical Debt)

#### 8. Test Infrastructure Improvements
**Status:** Enhancement Needed  
**Files Affected:**
- `test/features/booking/presentation/booking_confirmation_screen_test.dart:16`

**Problem:** Test code not using super parameters where possible.

**Impact:** Code style consistency, minor performance.

**Resolution Plan:**
1. Update test constructors to use super parameters
2. Review all test files for similar patterns
3. Update testing documentation

---

#### 9. Logging Migration Incomplete
**Status:** Partially Complete  
**Files Affected:**
- `lib/features/booking/presentation/booking_confirmation_screen.dart:159` (Fixed)

**Problem:** Some print statements still exist instead of using the logger service.

**Impact:** Inconsistent logging, harder debugging in production.

**Resolution Plan:**
1. ✅ Replace remaining print statements with debugPrint or logger
2. Complete migration to structured logging service
3. Add logging configuration for different environments

---

## Resolution Timeline

### Sprint 1 (Current - Week of May 25, 2025)
- [ ] Fix duplicate `userBookingsProvider` (Critical #1)
- [ ] Add missing route constants (Critical #2)
- [ ] Replace deprecated APIs (High Priority #4)

### Sprint 2 (Week of June 1, 2025)
- [ ] Complete TODO implementations (High Priority #5)
- [ ] Fix code quality issues (Medium Priority #6)
- [ ] Add BuildContext mounted checks (Medium Priority #7)

### Sprint 3 (Week of June 8, 2025)
- [ ] Improve test infrastructure (Low Priority #8)
- [ ] Complete logging migration (Low Priority #9)
- [ ] Comprehensive testing of all fixes

---

## Monitoring and Prevention

1. **Pre-commit Hooks:** Set up `flutter analyze` and basic linting checks
2. **CI/CD Pipeline:** Add automated testing for duplicate providers and missing constants
3. **Code Review Checklist:** Include checks for deprecated API usage and proper error handling
4. **Documentation:** Maintain this issues list and update as new issues are discovered
