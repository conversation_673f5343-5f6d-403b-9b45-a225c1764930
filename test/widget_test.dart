// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:skills/features/auth/presentation/login_page.dart';
import 'test_helpers/widget_test_helper.dart';

void main() {
  setUp(() async {
    // setupWidgetTest handles Supabase initialization with mocks and provider overrides
    await setupWidgetTest();
  });

  testWidgets('App redirects to LoginPage when not authenticated', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    // testableApp() uses provider overrides to inject the mocked Supabase client
    await tester.pumpWidget(testableApp());

    // Pump and settle to allow navigation to complete.
    await tester.pumpAndSettle();

    // Verify that the LoginPage is displayed.
    expect(find.byType(LoginPage), findsOneWidget);
    expect(find.text('Login'), findsWidgets); // AppBar title and Button text
  });
}
