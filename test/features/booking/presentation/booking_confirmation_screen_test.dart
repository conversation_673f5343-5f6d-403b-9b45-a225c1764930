import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';
import 'package:skills/features/booking/application/optimistic_booking_service.dart';
import 'package:skills/features/booking/presentation/booking_confirmation_screen.dart';
import 'package:skills/features/booking/presentation/my_bookings_screen.dart';

// Mocks
// Mock for OptimisticBookingService
class MockOptimisticBookingService
    extends AutoDisposeNotifier<OptimisticBookingState>
    with <PERSON><PERSON>
    implements OptimisticBookingService {
  @override
  OptimisticBookingState build() {
    return const OptimisticBookingState(status: OptimisticBookingStatus.idle);
  }

  // Expose a way to change the state for testing
  void setTestState(OptimisticBookingState newState) {
    state = newState;
  }
}

class MockNavigatorObserver extends Mock implements NavigatorObserver {}

class FakeRoute extends Fake implements Route<dynamic> {}

// A dummy home screen to act as a previous route
class DummyHomeScreen extends StatelessWidget {
  const DummyHomeScreen({super.key});
  static const routeName = 'dummy-home';
  static const routePath = '/dummy-home';

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('Dummy Home')));
  }
}

void main() {
  late MockOptimisticBookingService mockOptimisticBookingService;
  late MockNavigatorObserver mockNavigatorObserver;
  late GoRouter goRouter;

  const pitchIdStr = "1";
  const pitchName = "Test Pitch";
  final selectedDate = DateTime(2024, 6, 1);
  final slotStartTime = DateTime(2024, 6, 1, 10, 0);
  final slotEndTime = DateTime(2024, 6, 1, 11, 0);

  setUp(() {
    mockOptimisticBookingService = MockOptimisticBookingService();
    mockNavigatorObserver = MockNavigatorObserver();

    // Register fallback values for OptimisticBookingState
    registerFallbackValue(
      const OptimisticBookingState(status: OptimisticBookingStatus.idle),
    );
    registerFallbackValue(FakeRoute());

    // Set up default mock behavior
    when(
      () => mockOptimisticBookingService.createBookingOptimistic(
        pitchId: any(named: 'pitchId'),
        slotStartTime: any(named: 'slotStartTime'),
        slotEndTime: any(named: 'slotEndTime'),
      ),
    ).thenAnswer((invocation) async {
      // Default behavior: simulate loading state
      mockOptimisticBookingService.setTestState(
        const OptimisticBookingState(
          status: OptimisticBookingStatus.optimisticLoading,
        ),
      );
      return Future<void>.value();
    });

    goRouter = GoRouter(
      initialLocation: DummyHomeScreen.routePath,
      observers: [mockNavigatorObserver],
      routes: [
        GoRoute(
          path: DummyHomeScreen.routePath,
          name: DummyHomeScreen.routeName,
          builder: (context, state) => const DummyHomeScreen(),
        ),
        GoRoute(
          path: BookingConfirmationScreen.routePath,
          name: BookingConfirmationScreen.routeName,
          builder: (context, state) {
            return BookingConfirmationScreen(
              pitchId: pitchIdStr,
              pitchName: pitchName,
              selectedDate: selectedDate,
              slotStartTime: slotStartTime,
              slotEndTime: slotEndTime,
            );
          },
        ),
        GoRoute(
          name: MyBookingsScreen.routeName,
          path: MyBookingsScreen.routePath,
          builder: (context, state) => const MyBookingsScreen(),
        ),
      ],
    );
  });

  Widget createTestWidget() {
    return ProviderScope(
      overrides: [
        optimisticBookingServiceProvider.overrideWith(
          () => mockOptimisticBookingService,
        ),
      ],
      child: MaterialApp.router(routerConfig: goRouter),
    );
  }

  /// Helper to navigate to booking confirmation screen with proper stack
  Future<void> navigateToBookingConfirmation(WidgetTester tester) async {
    await tester.pumpWidget(createTestWidget());
    await tester.pumpAndSettle();

    // Navigate using push instead of go to maintain navigation stack
    goRouter.pushNamed(BookingConfirmationScreen.routeName);
    await tester.pumpAndSettle();
  }

  group('BookingConfirmationScreen', () {
    testWidgets('displays booking summary correctly', (
      WidgetTester tester,
    ) async {
      // Stub the methods that won't be called in this test
      when(
        () => mockOptimisticBookingService.createBookingOptimistic(
          pitchId: any(named: 'pitchId'),
          slotStartTime: any(named: 'slotStartTime'),
          slotEndTime: any(named: 'slotEndTime'),
        ),
      ).thenAnswer((_) async {});
      when(() => mockOptimisticBookingService.resetState()).thenReturn(null);

      await navigateToBookingConfirmation(tester);

      expect(find.text('Confirm Booking'), findsOneWidget);
      expect(find.text('Please confirm your booking details:'), findsOneWidget);
      expect(find.text('Pitch: Test Pitch'), findsOneWidget);
      expect(find.text('Date: Sat, Jun 1, 2024'), findsOneWidget);
      expect(find.text('Time: 10:00 - 11:00'), findsOneWidget);
      expect(find.text('Football included'), findsOneWidget);
      expect(find.text('Confirm & Proceed to Book'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets(
      'calls createBooking, shows loading, then success and navigates on confirm button tap',
      (WidgetTester tester) async {
        // Stub createBooking to simulate loading behavior
        when(
          () => mockOptimisticBookingService.createBookingOptimistic(
            pitchId: any(named: 'pitchId'),
            slotStartTime: any(named: 'slotStartTime'),
            slotEndTime: any(named: 'slotEndTime'),
          ),
        ).thenAnswer((_) async {
          mockOptimisticBookingService.setTestState(
            const OptimisticBookingState(
              status: OptimisticBookingStatus.optimisticLoading,
            ),
          );
        });

        when(() => mockOptimisticBookingService.resetState()).thenAnswer((_) {
          mockOptimisticBookingService.setTestState(
            const OptimisticBookingState(status: OptimisticBookingStatus.idle),
          );
        });

        await navigateToBookingConfirmation(tester);

        expect(find.byType(CircularProgressIndicator), findsNothing);
        expect(find.text('Confirm & Proceed to Book'), findsOneWidget);

        // Tap button. This calls createBookingOptimistic.
        await tester.tap(find.text('Confirm & Proceed to Book'));
        // Pump for UI to rebuild based on state change (now Loading).
        await tester.pump();

        expect(find.byType(CircularProgressIndicator), findsOneWidget);

        // Simulate the service's state changing to Success.
        mockOptimisticBookingService.setTestState(
          const OptimisticBookingState(status: OptimisticBookingStatus.success),
        );
        await tester.pump(); // Pump for listener to react (SnackBar, dialog)

        expect(find.text('Booking successful!'), findsAtLeastNWidgets(1));
        await tester.pumpAndSettle(); // Complete animations

        // Verify the success dialog appears
        expect(find.text('Booking Confirmed!'), findsOneWidget);
        expect(
          find.text('Your booking has been successfully created.'),
          findsOneWidget,
        );
        expect(find.text('Done'), findsOneWidget);
        expect(find.text('Book Another'), findsOneWidget);

        // Tap "Done" to close dialog and navigate back
        await tester.tap(find.text('Done'));
        await tester
            .pumpAndSettle(); // Complete dialog dismissal and navigation

        verify(
          () => mockOptimisticBookingService.createBookingOptimistic(
            pitchId: int.parse(pitchIdStr),
            slotStartTime: slotStartTime,
            slotEndTime: slotEndTime,
          ),
        ).called(1);
        verify(() => mockNavigatorObserver.didPop(any(), any())).called(2);
        verify(() => mockOptimisticBookingService.resetState()).called(1);
      },
    );

    testWidgets('shows error SnackBar when createBookingOptimistic fails', (
      WidgetTester tester,
    ) async {
      const errorMessage = 'Failed to create booking';

      // Stub createBookingOptimistic to simulate loading then error behavior
      when(
        () => mockOptimisticBookingService.createBookingOptimistic(
          pitchId: any(named: 'pitchId'),
          slotStartTime: any(named: 'slotStartTime'),
          slotEndTime: any(named: 'slotEndTime'),
        ),
      ).thenAnswer((_) async {
        mockOptimisticBookingService.setTestState(
          const OptimisticBookingState(
            status: OptimisticBookingStatus.optimisticLoading,
          ),
        );
      });

      when(() => mockOptimisticBookingService.resetState()).thenAnswer((_) {
        mockOptimisticBookingService.setTestState(
          const OptimisticBookingState(status: OptimisticBookingStatus.idle),
        );
      });

      await navigateToBookingConfirmation(tester);

      // Tap button. createBookingOptimistic mock sets state to Loading.
      await tester.tap(find.text('Confirm & Proceed to Book'));
      await tester.pump(); // UI rebuilds with Loading state

      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Simulate Error state
      mockOptimisticBookingService.setTestState(
        const OptimisticBookingState(
          status: OptimisticBookingStatus.error,
          message: errorMessage,
        ),
      );
      await tester.pump(); // Listener reacts to Error state (SnackBar)

      expect(find.text(errorMessage), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsNothing);

      await tester.pumpAndSettle(); // Settle animations

      verify(
        () => mockOptimisticBookingService.createBookingOptimistic(
          pitchId: int.parse(pitchIdStr),
          slotStartTime: slotStartTime,
          slotEndTime: slotEndTime,
        ),
      ).called(1);
      verify(() => mockOptimisticBookingService.resetState()).called(1);
    });

    testWidgets('navigates back on cancel button tap', (
      WidgetTester tester,
    ) async {
      // Stub methods that won't be called in this test
      when(
        () => mockOptimisticBookingService.createBookingOptimistic(
          pitchId: any(named: 'pitchId'),
          slotStartTime: any(named: 'slotStartTime'),
          slotEndTime: any(named: 'slotEndTime'),
        ),
      ).thenAnswer((_) async {});
      when(() => mockOptimisticBookingService.resetState()).thenReturn(null);

      await navigateToBookingConfirmation(tester);

      expect(find.text('Confirm Booking'), findsOneWidget);

      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      verify(() => mockNavigatorObserver.didPop(any(), any())).called(1);
      expect(find.text('Dummy Home'), findsOneWidget);
      expect(find.text('Confirm Booking'), findsNothing);
    });
  });
}
