# Clean TDD Implementation Plan - Database-Centric Slot Management

**Date:** June 18, 2025  
**Approach:** Clean removal → Thoughtful test design → TDD implementation  
**Goal:** Simple app that books slots and shows bookings (database as single source of truth)

## 🧹 **Phase 1: Clean Removal (15 minutes)**

### **Files to Remove/Simplify:**

#### **1. Remove Complex Client Logic**
- **File:** `real_time_availability_provider.dart`
- **Remove:** `_calculateAvailableSlots()` function (~150 lines of complex DateTime logic)
- **Remove:** `_parseTimeOfDay()` helper function
- **Keep:** Stream structure for real-time updates
- **Simplify:** To pure database query wrapper

#### **2. Clean Up Test Files**
- **File:** `test/features/availability/application/available_slots_provider_test.dart`
- **Action:** Archive current tests (rename to `.old` extension)
- **Reason:** These test the buggy client-side calculation logic

#### **3. Remove Debug Files** 
- **File:** `lib/debug/availability_debug_helper.dart`
- **File:** `lib/debug/slot_booking_debugger.dart`  
- **File:** `docs/DEBUGGING_MISSING_SLOT.md`
- **Action:** Archive to `docs/archived/` folder

### **Dependencies to Update:**
- Update any files importing the removed functions
- Ensure UI still compiles (temporarily return empty lists if needed)

## 🎯 **Phase 2: Test Design - Pseudo Code (30 minutes)**

Let's think through our tests BEFORE writing any implementation:

### **Core Principle: Database as Single Source of Truth**
```
App Behavior:
1. User views availability → Fetch slots from database  
2. User books slot → Create booking + Update slot status in database
3. Real-time updates → Stream changes from database
4. Slot generation → Database function (not client-side)
```

### **Test Categories to Design:**

#### **A. Database Slot Provider Tests**
```pseudo
GIVEN a database with time_slots table
WHEN I request slots for a pitch and date
THEN I should get correctly formatted TimeSlotInfo objects

Test Cases:
✓ should_fetch_slots_for_valid_pitch_and_date()
  - Mock database returning slot records
  - Verify TimeSlotInfo objects created correctly
  - Verify date/time parsing works

✓ should_return_empty_list_when_no_slots_exist()
  - Mock database returning empty result
  - Verify graceful handling

✓ should_handle_database_errors_gracefully()
  - Mock database throwing exception
  - Verify error handling (maybe return cached data or empty list)

✓ should_watch_slot_changes_in_realtime()
  - Mock database stream
  - Verify UI updates when slot status changes
  - Verify when new booking created, slot becomes booked

✓ should_auto_generate_slots_when_none_exist()
  - Mock database with no slots for future date
  - Verify automatic slot generation triggered
  - Verify slots created based on pitch settings
```

#### **B. Database Integration Tests**
```pseudo
GIVEN a real database connection (integration test)
WHEN I perform slot operations
THEN database state should be correct

Test Cases:
✓ should_generate_slots_correctly_for_pitch_settings()
  - Create pitch with specific open/close times
  - Call slot generation function
  - Verify correct number of slots created
  - Verify start/end times are correct

✓ should_update_slot_status_when_booking_created()
  - Create slots for a date
  - Create booking for specific slot
  - Verify slot.is_booked = true
  - Verify slot.booking_id = booking.id

✓ should_update_slot_status_when_booking_deleted()
  - Create booked slot
  - Delete the booking
  - Verify slot.is_booked = false
  - Verify slot.booking_id = null

✓ should_prevent_double_booking_at_database_level()
  - Try to book the same slot twice
  - Verify database constraint prevents it
  - Verify error handling
```

#### **C. TimeSlotInfo Model Tests**
```pseudo
GIVEN database JSON data
WHEN I create TimeSlotInfo objects
THEN data should be parsed correctly

Test Cases:
✓ should_parse_database_json_correctly()
  - Valid JSON with all fields
  - Verify all properties set correctly
  - Verify date/time conversion works

✓ should_handle_missing_optional_fields()
  - JSON missing booking_id (null)
  - Verify defaults applied correctly

✓ should_parse_time_strings_correctly()
  - Various time formats from database
  - Verify DateTime objects created correctly
  - Test edge cases (midnight, etc.)
```

#### **D. UI Integration Tests**
```pseudo
GIVEN the new database provider
WHEN user interacts with availability screen
THEN correct behavior should occur

Test Cases:
✓ should_display_slots_from_database()
  - Mock database provider with test slots
  - Verify slots rendered in UI
  - Verify booked/available styling correct

✓ should_update_ui_when_slot_status_changes()
  - Mock slot status change in database stream
  - Verify UI updates automatically
  - No manual refresh needed

✓ should_handle_database_errors_in_ui()
  - Mock database provider error
  - Verify graceful error display
  - No app crashes
```

#### **E. Bug Fix Validation Tests**
```pseudo
GIVEN the old system had 3-slot offset bug
WHEN I use new database system  
THEN correct slot should show as booked

Test Cases:
✓ should_mark_correct_slot_as_booked()
  - Create booking for slot at specific time
  - Verify EXACT SAME slot shows as booked in UI
  - Not 3 slots before or after

✓ should_maintain_booking_accuracy_across_multiple_bookings()
  - Create multiple bookings
  - Verify each booking marks correct slot
  - No offset errors
```

### **Test Data Strategy:**
```pseudo
Test Pitch Settings:
- Open: 09:00, Close: 17:00, Duration: 60min = 8 slots per day
- This gives predictable, easy-to-verify slot generation

Test Dates:
- Use fixed date: 2025-06-20 (avoid timezone complications in tests)
- Use future dates to avoid "past slot" logic complications

Test Bookings:
- Slot 0: 09:00-10:00
- Slot 3: 12:00-13:00  
- Slot 7: 16:00-17:00
- This gives clear spacing for offset bug detection
```

## 🔨 **Phase 3: TDD Implementation (2-3 hours)**

### **Step 1: Write Failing Tests First**
1. Create `database_slot_provider_test.dart` with failing tests
2. Create `time_slot_info_model_test.dart` with failing tests  
3. Run tests → Everything should fail (no implementation yet)

### **Step 2: Implement Database Schema**
1. Create `time_slots` table
2. Create slot generation function
3. Create booking status trigger
4. Run integration tests → Database tests should pass

### **Step 3: Implement Flutter Provider**
1. Create `DatabaseSlotProvider` 
2. Implement `TimeSlotInfo.fromDatabaseJson()`
3. Run tests → Model and provider tests should pass

### **Step 4: Update UI**
1. Update `AvailabilityDetails` to use database provider
2. Add feature flag for safe rollout
3. Run UI tests → Should pass

### **Step 5: Validation**
1. Run bug fix test → Verify 3-slot offset is gone
2. Manual testing → Create booking, verify correct slot marked
3. Performance testing → Verify <100ms load times

## 📋 **Success Criteria**

### **Functional:**
- ✅ Availability screen loads slots from database
- ✅ Creating booking immediately updates slot status  
- ✅ Correct slot shows as booked (no 3-slot offset)
- ✅ Real-time updates work across multiple clients
- ✅ App is simpler (no complex client calculations)

### **Technical:**
- ✅ All tests pass (TDD approach ensures this)
- ✅ Database is single source of truth
- ✅ No client-side slot calculation logic
- ✅ Clean, maintainable code
- ✅ Easy to add new features (dynamic pricing, etc.)

### **Performance:**
- ✅ Slot loading < 100ms
- ✅ Real-time updates < 2 seconds
- ✅ No database performance issues

## 🚀 **Implementation Order**

1. **Clean removal** (15 min) - Remove buggy code
2. **Design tests** (30 min) - Think through test cases  
3. **Write failing tests** (30 min) - TDD red phase
4. **Database implementation** (60 min) - TDD green phase
5. **Flutter implementation** (60 min) - TDD green phase
6. **UI integration** (30 min) - Connect everything
7. **Validation** (15 min) - Verify bug is fixed

**Total: ~4 hours of focused, clean implementation**

---

**Philosophy:** Start clean, think carefully, test first, implement minimally.  
**Result:** Simple, reliable slot booking system with database as single source of truth.
