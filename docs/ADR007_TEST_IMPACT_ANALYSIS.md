# ADR-007 Test Impact Analysis

**Date:** June 18, 2025  
**Scope:** Tests affected by database-centric slot management migration  

## 🚨 Tests That Need Complete Rewrite

### 1. `/test/features/availability/application/available_slots_provider_test.dart`
**Status:** 🔴 **COMPLETE REWRITE REQUIRED**  
**Current Test Count:** 11 test cases  
**Impact:** HIGH - Tests core slot calculation logic that's being replaced

#### What It Currently Tests:
- **Slot Generation Logic:** Tests client-side slot calculation based on pitch settings
- **Booking Status Calculation:** Tests how bookings affect slot availability  
- **Edge Cases:** Open/close time validation, slot duration edge cases
- **Error Handling:** Pitch settings failures, booking fetch failures

#### Why Complete Rewrite Needed:
```dart
// Current test approach - tests client-side calculation
testWidgets('Happy Path - Basic availability (no bookings)', (tester) async {
  // Tests that slots are generated correctly from pitch settings
  // Tests that no bookings = all slots available
  // This entire logic moves to database
});

testWidgets('Booking at the start of the day', (tester) async {
  // Tests client-side booking status calculation
  // Tests DateTime comparison logic (the buggy part!)
  // Database triggers will handle this automatically
});
```

#### New Tests Needed:
```dart
// New database-focused tests
group('Database Slot Management', () {
  test('should fetch slots from database correctly', () async {
    // Test DatabaseSlotProvider fetching from time_slots table
  });

  test('should generate slots when none exist', () async {
    // Test automatic slot generation for new dates
  });

  test('should sync real-time updates from database', () async {
    // Test Stream updates when slots change
  });

  test('should handle database errors gracefully', () async {
    // Test fallback behavior when database unavailable
  });
});
```

### 2. Tests in `/test/features/availability/presentation/availability_screen_widget_test.dart`
**Status:** 🟡 **PARTIAL REWRITE REQUIRED**  
**Current Test Count:** 14 test cases  
**Impact:** MEDIUM - Tests UI that consumes slot data

#### Tests That Need Updates:
- **Slot Display Tests:** Need to mock database provider instead of calculation logic
- **Real-time Update Tests:** Need to test database stream updates
- **Error Handling Tests:** Need to test database error scenarios

#### Tests That Stay the Same:
- **UI Rendering Tests:** Basic widget rendering doesn't change
- **Date Selection Tests:** Date picker functionality unchanged
- **Pitch Selection Tests:** Pitch dropdown functionality unchanged

#### Example Changes Needed:
```dart
// OLD: Mock calculation-based provider
realTimeAvailabilityProvider.overrideWith((ref) async => mockSlots);

// NEW: Mock database provider AND feature flag
useDatabaseSlotsProvider.overrideWith((ref) => true),
databaseSlotProvider.overrideWith((ref) => mockDatabaseSlots);
```

### 3. Tests in `/test/features/availability/presentation/availability_screen_test.dart`  
**Status:** 🟡 **PARTIAL REWRITE REQUIRED**  
**Current Test Count:** ~8 test cases  
**Impact:** MEDIUM - Integration tests for availability screen

#### Changes Needed:
- Update provider mocking to include database providers
- Add feature flag testing
- Test hybrid mode (both old and new systems)

## 🟢 Tests That Need Minor Updates

### 4. `/test/features/availability/presentation/booking_limit_widget_test.dart`
**Status:** 🟢 **MINOR UPDATES ONLY**  
**Impact:** LOW - Uses TimeSlotInfo but doesn't test slot generation

#### Changes Needed:
- Update TimeSlotInfo creation to include new database fields
- No logic changes needed

```dart
// OLD TimeSlotInfo creation
TimeSlotInfo(
  startTime: startTime,
  endTime: endTime,
  isBooked: false,
  price: 5000.0,
);

// NEW TimeSlotInfo creation (add new fields)
TimeSlotInfo(
  startTime: startTime,
  endTime: endTime,
  isBooked: false,
  price: 5000.0,
  id: 'test-id',           // NEW
  isAvailable: true,       // NEW  
  bookingId: null,         // NEW
);
```

## 📋 New Tests Required

### 5. NEW: `/test/features/availability/application/database_slot_provider_test.dart`
**Status:** 🔵 **NEW TEST FILE REQUIRED**  
**Priority:** HIGH - Tests new core functionality

#### Test Cases Needed:
```dart
group('DatabaseSlotProvider', () {
  test('should fetch slots from database correctly', () async {
    // Test basic slot fetching
  });

  test('should watch slots with real-time updates', () async {
    // Test Stream subscription
  });

  test('should generate slots when none exist for date', () async {
    // Test auto-generation logic
  });

  test('should handle database connection errors', () async {
    // Test error handling and fallback
  });

  test('should filter by pitch and date correctly', () async {
    // Test query parameters
  });
});
```

### 6. NEW: `/test/features/availability/domain/time_slot_info_model_test.dart`
**Status:** 🔵 **NEW TEST FILE REQUIRED**  
**Priority:** MEDIUM - Tests model serialization

#### Test Cases Needed:
```dart
group('TimeSlotInfo Model', () {
  test('should create from database JSON correctly', () {
    // Test fromDatabaseJson factory
  });

  test('should handle missing optional fields', () {
    // Test null safety in deserialization
  });

  test('should parse time strings correctly', () {
    // Test time parsing logic
  });
});
```

### 7. NEW: Integration Tests for Hybrid System
**Status:** 🔵 **NEW TEST CATEGORY REQUIRED**  
**Priority:** HIGH - Critical for safe migration

#### Test Cases Needed:
```dart
group('Hybrid Slot System', () {
  test('should switch between old and new systems via feature flag', () {
    // Test feature flag switching
  });

  test('should produce same results from both systems', () {
    // Test data consistency during migration
  });

  test('should fallback to old system if database fails', () {
    // Test resilience during migration
  });
});
```

## 🛠️ Test Helper Updates Required

### Test Helpers That Need Updates:
1. **`test_helpers/widget_test_helper.dart`**
   - Add database provider mocking utilities
   - Add feature flag override helpers

2. **`test_helpers/supabase_mock_helper.dart`**
   - Add time_slots table mocking
   - Add database stream mocking

### New Test Helpers Needed:
```dart
// New: test_helpers/database_slot_test_helper.dart
class DatabaseSlotTestHelper {
  static List<Map<String, dynamic>> createDatabaseSlots({
    required String pitchId,
    required DateTime date,
    int count = 5,
  }) {
    // Helper to create mock database slot data
  }

  static void mockDatabaseSlotProvider(
    ProviderContainer container,
    List<TimeSlotInfo> slots,
  ) {
    // Helper to mock database provider responses
  }
}
```

## ⏱️ Implementation Timeline

### Phase 1: Critical Test Updates (30 minutes)
- ✅ Update `available_slots_provider_test.dart` to test database provider
- ✅ Add basic `database_slot_provider_test.dart`

### Phase 2: UI Test Updates (30 minutes)  
- ✅ Update widget tests to mock database providers
- ✅ Add feature flag testing

### Phase 3: New Test Coverage (60 minutes)
- ✅ Add comprehensive database provider tests  
- ✅ Add model serialization tests
- ✅ Add hybrid system tests

### Phase 4: Test Helpers (30 minutes)
- ✅ Create database test helpers
- ✅ Update existing test utilities

## 🎯 Test Strategy Summary

### Total Test Files Affected: 4 existing + 3 new = 7 files

#### Complete Rewrite Required (1 file):
- `available_slots_provider_test.dart` - **Core business logic changed**

#### Partial Updates Required (3 files):
- `availability_screen_widget_test.dart` - **Provider mocking changes**
- `availability_screen_test.dart` - **Integration test updates**  
- `booking_limit_widget_test.dart` - **Model field additions**

#### New Tests Required (3 files):
- `database_slot_provider_test.dart` - **New provider tests**
- `time_slot_info_model_test.dart` - **Model serialization tests**
- Hybrid system integration tests - **Migration safety tests**

### Risk Mitigation:
- **Keep old tests during migration** - Don't delete until new system proven
- **Add comparison tests** - Validate both systems produce same results
- **Feature flag tests** - Ensure safe switching between systems
- **Database fallback tests** - Test resilience when database unavailable

---

**Recommendation:** Start with the critical test updates (Phase 1) before implementing ADR-007, then add the comprehensive test coverage in parallel with the implementation.
