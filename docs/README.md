# Documentation Index - Skills Football Pitch Booking App

This directory contains all project documentation organized by purpose.

## 📊 **Quick Navigation**

### **For Developers Starting Today**
1. **[Development Guidelines](development_guidelines.md)** - Setup, coding standards, and best practices
2. **[Project Status Dashboard](PROJECT_STATUS.md)** - Current progress and what to work on next
3. **[Known Issues](known_issues.md)** - Current bugs and their resolution plans

### **For Understanding Architecture**
1. **[ADRs Directory](adrs/)** - All architectural decisions with rationale
2. **[Project Plan](Football%20Pitch%20Booking%20App_%20Project%20Plan%20&%20Develo....md)** - Complete requirements and technical specifications

### **For Implementation Details**
1. **[Development Notes](dev_notes/)** - Session logs and implementation journey
2. **[Database Migrations](database_migrations/)** - Schema changes and migration scripts

## 📋 **Document Types Explained**

| Type | Purpose | When to Read | When to Update |
|------|---------|--------------|----------------|
| **ADRs** | Record major technical decisions | Before changing architecture | When making significant technical choices |
| **Dev Notes** | Track daily progress and discoveries | To understand recent changes | After each development session |
| **Status Dashboard** | High-level progress tracking | Weekly check-ins | Weekly or after milestones |
| **Known Issues** | Bug tracking and resolution | Before starting new work | When discovering or fixing bugs |
| **Project Plan** | Requirements and roadmap | Planning new features | When requirements change |

## 🔄 **Maintenance Guidelines**

### **Keep Current**
- **PROJECT_STATUS.md** - Update weekly with progress percentages
- **dev_notes/dev_notes-current.md** - Update after each coding session
- **known_issues.md** - Update when bugs are found or fixed

### **Archive When Full**
- **Development Notes** - Archive when current file reaches ~500 lines
- **Sprint Reviews** - Create milestone summaries in dev_notes

### **Update on Major Changes**
- **ADRs** - Create new ADR for significant architectural decisions
- **Project Plan** - Update when requirements or scope changes
- **Development Guidelines** - Update when processes or standards change

---

**Last Updated**: June 12, 2025  
**Next Review**: June 19, 2025
