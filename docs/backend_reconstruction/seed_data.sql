-- =====================================================
-- SEED DATA FOR SUPABASE RECONSTRUCTION
-- =====================================================
-- Date: June 20, 2025
-- Purpose: Initialize database with test data for ADR-007 validation
-- Critical: Enables immediate testing of 3-slot offset bug fix

-- =====================================================
-- PITCH SETTINGS (Test Data)
-- =====================================================

-- Insert test pitch configurations
INSERT INTO pitch_settings (
  pitch_name,
  open_time,
  close_time,
  slot_duration_minutes,
  cancellation_window_hours,
  price_per_hour,
  max_bookings_per_user
) VALUES 
  (
    'Main Football Pitch',
    '06:00:00',
    '22:00:00',
    60, -- 1 hour slots
    2,  -- 2 hours cancellation window
    25.00, -- 25 MWK per hour
    4   -- Max 4 bookings per user
  ),
  (
    'Training Ground A',
    '07:00:00',
    '20:00:00',
    90, -- 1.5 hour slots
    4,  -- 4 hours cancellation window
    20.00, -- 20 MWK per hour
    3   -- Max 3 bookings per user
  ),
  (
    'Mini Pitch B',
    '08:00:00',
    '18:00:00',
    30, -- 30 minute slots
    1,  -- 1 hour cancellation window
    15.00, -- 15 MWK per hour
    6   -- Max 6 bookings per user
  );

-- =====================================================
-- GENERATE INITIAL TIME SLOTS (ADR-007 Critical)
-- =====================================================

-- Generate slots for the next 7 days for all pitches
-- This validates the ADR-007 slot generation functionality
DO $$
DECLARE
  pitch_record RECORD;
  date_record DATE;
  total_slots INTEGER := 0;
  result_record RECORD;
BEGIN
  RAISE NOTICE 'Starting ADR-007 slot generation for next 7 days...';
  
  FOR pitch_record IN SELECT id, pitch_name FROM pitch_settings LOOP
    RAISE NOTICE 'Generating slots for pitch: % (ID: %)', pitch_record.pitch_name, pitch_record.id;
    
    FOR date_record IN 
      SELECT generate_series(CURRENT_DATE, CURRENT_DATE + INTERVAL '6 days', '1 day')::DATE
    LOOP
      -- Generate slots for current date
      SELECT * INTO result_record 
      FROM generate_time_slots(pitch_record.id, date_record, false);
      
      total_slots := total_slots + result_record.slots_created;
      
      RAISE NOTICE '  Date %: % slots created', date_record, result_record.slots_created;
    END LOOP;
  END LOOP;
  
  RAISE NOTICE 'ADR-007 slot generation complete. Total slots created: %', total_slots;
END;
$$;

-- =====================================================
-- TEST BOOKINGS (For ADR-007 Validation)
-- =====================================================

-- Note: These bookings require actual user IDs from auth.users
-- They will be created during integration testing phase
-- This section documents the test booking scenarios needed

/*
-- Test booking scenarios for ADR-007 validation:

-- Scenario 1: Single booking (should mark 1 slot as booked)
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES (
  'USER_ID_HERE', -- Replace with actual user ID
  1, -- Main Football Pitch
  CURRENT_DATE + INTERVAL '1 day' + TIME '10:00:00',
  CURRENT_DATE + INTERVAL '1 day' + TIME '11:00:00',
  'confirmed'
);

-- Scenario 2: Multiple bookings (should mark multiple slots as booked)
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES 
  (
    'USER_ID_HERE',
    1,
    CURRENT_DATE + INTERVAL '2 days' + TIME '14:00:00',
    CURRENT_DATE + INTERVAL '2 days' + TIME '15:00:00',
    'confirmed'
  ),
  (
    'USER_ID_HERE',
    1,
    CURRENT_DATE + INTERVAL '2 days' + TIME '16:00:00',
    CURRENT_DATE + INTERVAL '2 days' + TIME '17:00:00',
    'confirmed'
  );

-- Scenario 3: Cancelled booking (should mark slot as available)
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES (
  'USER_ID_HERE',
  2,
  CURRENT_DATE + INTERVAL '3 days' + TIME '09:00:00',
  CURRENT_DATE + INTERVAL '3 days' + TIME '10:30:00',
  'cancelledByUser'
);
*/

-- =====================================================
-- VALIDATION QUERIES
-- =====================================================

-- Query to verify pitch settings were created
SELECT 
  id,
  pitch_name,
  open_time,
  close_time,
  slot_duration_minutes,
  price_per_hour,
  max_bookings_per_user
FROM pitch_settings
ORDER BY id;

-- Query to verify time slots were generated
SELECT 
  pitch_id,
  slot_date,
  COUNT(*) as slots_count,
  COUNT(*) FILTER (WHERE is_booked = true) as booked_count,
  COUNT(*) FILTER (WHERE is_available = true) as available_count
FROM time_slots
GROUP BY pitch_id, slot_date
ORDER BY pitch_id, slot_date;

-- Query to verify ADR-007 slot generation function works
SELECT * FROM generate_time_slots(1, CURRENT_DATE + INTERVAL '8 days', false);

-- Query to test slot statistics function
SELECT * FROM get_slot_stats(1, CURRENT_DATE, CURRENT_DATE + INTERVAL '7 days');

-- =====================================================
-- ENVIRONMENT CONFIGURATION NOTES
-- =====================================================

/*
Required Environment Variables for Flutter App:
- SUPABASE_URL: Your new Supabase project URL
- SUPABASE_ANON_KEY: Your new Supabase anon key

Required Supabase Configuration:
1. Authentication Settings:
   - Enable email/password authentication
   - Configure email templates if needed
   - Set up redirect URLs for magic links

2. API Settings:
   - Enable auto-generated REST API
   - Enable Realtime for time_slots table
   - Configure CORS if needed

3. Database Settings:
   - Enable Row Level Security (RLS)
   - Configure connection pooling if needed
   - Set up database backups

4. Storage (if used):
   - Create storage buckets for user avatars
   - Configure storage policies
*/

-- =====================================================
-- ADR-007 INTEGRATION TEST QUERIES
-- =====================================================

-- Test 1: Verify slot generation creates correct number of slots
-- Expected: Should create slots based on pitch settings
SELECT 
  ps.pitch_name,
  ps.open_time,
  ps.close_time,
  ps.slot_duration_minutes,
  COUNT(ts.*) as generated_slots,
  -- Calculate expected slots
  EXTRACT(EPOCH FROM (ps.close_time - ps.open_time)) / (ps.slot_duration_minutes * 60) as expected_slots
FROM pitch_settings ps
LEFT JOIN time_slots ts ON ps.id = ts.pitch_id AND ts.slot_date = CURRENT_DATE
GROUP BY ps.id, ps.pitch_name, ps.open_time, ps.close_time, ps.slot_duration_minutes
ORDER BY ps.id;

-- Test 2: Verify trigger updates slot status correctly
-- This will be tested with actual bookings during integration phase

-- Test 3: Verify real-time streaming works
-- This will be tested from Flutter app during integration phase

-- =====================================================
-- CLEANUP COMMANDS (For Testing)
-- =====================================================

/*
-- Commands to reset data during testing:

-- Clear all time slots
DELETE FROM time_slots;

-- Clear all bookings  
DELETE FROM bookings;

-- Clear all profiles
DELETE FROM profiles;

-- Reset pitch settings to defaults
DELETE FROM pitch_settings;

-- Regenerate everything
-- (Re-run this seed script)
*/

-- =====================================================
-- SUCCESS CONFIRMATION
-- =====================================================

DO $$
DECLARE
  pitch_count INTEGER;
  slot_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO pitch_count FROM pitch_settings;
  SELECT COUNT(*) INTO slot_count FROM time_slots;
  
  RAISE NOTICE '=== SEED DATA SUMMARY ===';
  RAISE NOTICE 'Pitch settings created: %', pitch_count;
  RAISE NOTICE 'Time slots generated: %', slot_count;
  RAISE NOTICE 'ADR-007 database reconstruction ready for testing';
  RAISE NOTICE '========================';
END;
$$;
