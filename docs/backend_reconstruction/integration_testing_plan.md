# Integration Testing Plan - Backend Reconstruction
**Date:** June 20, 2025  
**Purpose:** Validate ADR-007 functionality with new Supabase backend  
**Critical:** Ensure 3-slot offset bug fix still works

---

## 🎯 **Testing Objectives**

### **Primary Goals**
1. **Validate ADR-007 Implementation**: Confirm database-centric slot management works
2. **Verify 3-Slot Offset Fix**: Ensure bug remains fixed with new backend
3. **Test Real-time Streaming**: Confirm live updates function correctly
4. **Validate Flutter Integration**: Ensure app connects and functions properly

### **Success Criteria**
- ✅ All ADR-007 test cases pass (11/11 tests)
- ✅ No slot offset issues detected
- ✅ Real-time updates work within 2 seconds
- ✅ Flutter app functions identically to pre-failure state

---

## 🧪 **Test Suite 1: Database Functionality**

### **Test 1.1: Schema Validation** (5 minutes)
**Objective**: Verify all required database components exist

**Test Steps**:
```sql
-- 1. Check all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'pitch_settings', 'bookings', 'time_slots')
ORDER BY table_name;

-- 2. Check ADR-007 functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_name IN ('generate_time_slots', 'update_slot_booking_status', 'generate_slots_bulk');

-- 3. Check critical trigger exists
SELECT trigger_name FROM information_schema.triggers 
WHERE trigger_name = 'booking_slot_status_trigger';
```

**Expected Results**:
- 4 tables found: bookings, pitch_settings, profiles, time_slots
- 3 functions found: generate_time_slots, update_slot_booking_status, generate_slots_bulk
- 1 trigger found: booking_slot_status_trigger

### **Test 1.2: Slot Generation Function** (10 minutes)
**Objective**: Validate ADR-007 slot generation works correctly

**Test Steps**:
```sql
-- 1. Test slot generation for tomorrow
SELECT * FROM generate_time_slots(1, CURRENT_DATE + INTERVAL '1 day', false);

-- 2. Verify slots were created
SELECT COUNT(*) as slot_count 
FROM time_slots 
WHERE pitch_id = 1 AND slot_date = CURRENT_DATE + INTERVAL '1 day';

-- 3. Check slot timing accuracy
SELECT 
  start_time,
  end_time,
  EXTRACT(EPOCH FROM (end_time - start_time))/60 as duration_minutes
FROM time_slots 
WHERE pitch_id = 1 AND slot_date = CURRENT_DATE + INTERVAL '1 day'
ORDER BY start_time
LIMIT 5;
```

**Expected Results**:
- Function returns success message with slot count
- Slot count matches expected number based on pitch settings
- All slots have correct duration (60 minutes for pitch 1)

### **Test 1.3: Booking Status Trigger** (15 minutes)
**Objective**: Verify trigger updates slot status correctly (ADR-007 critical)

**Prerequisites**: Create test user through Flutter app first

**Test Steps**:
```sql
-- 1. Create test booking
INSERT INTO bookings (
  user_id,
  pitch_id,
  slot_start_time,
  slot_end_time,
  status
) VALUES (
  '[TEST_USER_ID]', -- Replace with actual user ID
  1,
  CURRENT_DATE + INTERVAL '1 day' + TIME '14:00:00',
  CURRENT_DATE + INTERVAL '1 day' + TIME '15:00:00',
  'confirmed'
);

-- 2. Verify slot was marked as booked
SELECT is_booked, booking_id 
FROM time_slots 
WHERE pitch_id = 1 
  AND slot_date = CURRENT_DATE + INTERVAL '1 day'
  AND start_time = '14:00:00';

-- 3. Test cancellation
UPDATE bookings 
SET status = 'cancelledByUser' 
WHERE pitch_id = 1 
  AND slot_start_time = CURRENT_DATE + INTERVAL '1 day' + TIME '14:00:00';

-- 4. Verify slot was marked as available
SELECT is_booked, booking_id 
FROM time_slots 
WHERE pitch_id = 1 
  AND slot_date = CURRENT_DATE + INTERVAL '1 day'
  AND start_time = '14:00:00';
```

**Expected Results**:
- After booking creation: is_booked = true, booking_id = [booking_id]
- After cancellation: is_booked = false, booking_id = null

---

## 📱 **Test Suite 2: Flutter App Integration**

### **Test 2.1: Basic Connectivity** (10 minutes)
**Objective**: Verify Flutter app connects to new backend

**Test Steps**:
1. **Launch Flutter App**:
   ```bash
   flutter run
   ```

2. **Test Authentication**:
   - Navigate to sign-up screen
   - Create test account: `<EMAIL>` / `TestPass123!`
   - Verify email confirmation (check Supabase Auth logs)
   - Test login with created credentials

3. **Test Database Access**:
   - Navigate to availability screen
   - Verify pitch settings load
   - Check console for any connection errors

**Expected Results**:
- App launches without errors
- Authentication flows work correctly
- Pitch settings display properly
- No connection errors in logs

### **Test 2.2: Availability Screen Functionality** (15 minutes)
**Objective**: Validate core availability features work

**Test Steps**:
1. **Navigate to Availability Screen**
2. **Test Pitch Selection**:
   - Verify dropdown shows all pitches
   - Select different pitches
   - Confirm slots update for each pitch

3. **Test Date Selection**:
   - Change date to tomorrow
   - Verify slots load for new date
   - Test multiple date changes

4. **Test Slot Display**:
   - Verify slots show correct times
   - Check that booked slots (from Test 1.3) show as unavailable
   - Confirm available slots show "Book" button

**Expected Results**:
- All pitches appear in dropdown
- Slots load correctly for each pitch/date combination
- Booked slots show as unavailable
- Available slots show booking option

### **Test 2.3: Real-time Updates** (10 minutes)
**Objective**: Verify real-time streaming works

**Test Steps**:
1. **Open Flutter App** on availability screen
2. **In Supabase SQL Editor**, create new booking:
   ```sql
   INSERT INTO bookings (
     user_id,
     pitch_id,
     slot_start_time,
     slot_end_time,
     status
   ) VALUES (
     '[TEST_USER_ID]',
     1,
     CURRENT_DATE + INTERVAL '1 day' + TIME '16:00:00',
     CURRENT_DATE + INTERVAL '1 day' + TIME '17:00:00',
     'confirmed'
   );
   ```

3. **Observe Flutter App**:
   - Watch for slot status change
   - Time the update delay
   - Verify correct slot becomes unavailable

**Expected Results**:
- Slot status updates within 2-3 seconds
- Correct slot (16:00-17:00) becomes unavailable
- No other slots affected

---

## 🔍 **Test Suite 3: ADR-007 Specific Validation**

### **Test 3.1: 3-Slot Offset Bug Validation** (20 minutes)
**Objective**: Confirm the critical bug fix still works

**Test Steps**:
1. **Create Multiple Test Bookings**:
   ```sql
   -- Create bookings at specific times
   INSERT INTO bookings (user_id, pitch_id, slot_start_time, slot_end_time, status) VALUES
   ('[TEST_USER_ID]', 1, CURRENT_DATE + INTERVAL '2 days' + TIME '09:00:00', CURRENT_DATE + INTERVAL '2 days' + TIME '10:00:00', 'confirmed'),
   ('[TEST_USER_ID]', 1, CURRENT_DATE + INTERVAL '2 days' + TIME '11:00:00', CURRENT_DATE + INTERVAL '2 days' + TIME '12:00:00', 'confirmed'),
   ('[TEST_USER_ID]', 1, CURRENT_DATE + INTERVAL '2 days' + TIME '15:00:00', CURRENT_DATE + INTERVAL '2 days' + TIME '16:00:00', 'confirmed');
   ```

2. **In Flutter App**:
   - Navigate to day after tomorrow
   - Verify exact slots are marked as booked:
     - 09:00-10:00 ✅ Should be booked
     - 10:00-11:00 ✅ Should be available
     - 11:00-12:00 ✅ Should be booked
     - 12:00-13:00 ✅ Should be available
     - 15:00-16:00 ✅ Should be booked

3. **Verify No Offset**:
   - Confirm booked slots match exact booking times
   - Verify no 3-slot shift in either direction
   - Test with different pitch settings (30-min slots)

**Expected Results**:
- Booked slots match exactly with booking times
- No offset detected in any direction
- Different slot durations work correctly

### **Test 3.2: DatabaseSlotProvider Functionality** (15 minutes)
**Objective**: Validate ADR-007 provider integration

**Test Steps**:
1. **Run ADR-007 Test Suite**:
   ```bash
   flutter test test/features/availability/application/adr_007_validation_test.dart
   ```

2. **Verify All Tests Pass**:
   - TimeSlotInfo.fromDatabaseJson tests (3/3)
   - 3-Slot Offset Bug Fix tests (2/2)
   - DatabaseSlotException tests (2/2)
   - Enhanced Features tests (4/4)

3. **Test Provider Methods**:
   - Test `fetchSlots()` method
   - Test `watchSlots()` streaming
   - Test `generateSlots()` function

**Expected Results**:
- All 11 ADR-007 tests pass
- Provider methods work without errors
- Real-time streaming functions correctly

---

## 🚨 **Test Suite 4: Error Handling & Edge Cases**

### **Test 4.1: Connection Failure Handling** (10 minutes)
**Objective**: Verify app handles backend issues gracefully

**Test Steps**:
1. **Simulate Network Issues**:
   - Disconnect internet temporarily
   - Navigate to availability screen
   - Observe error handling

2. **Test Invalid Data**:
   - Try to create booking for past date
   - Test with invalid pitch ID
   - Verify appropriate error messages

**Expected Results**:
- App shows appropriate error messages
- No crashes or unhandled exceptions
- Graceful degradation of functionality

### **Test 4.2: Concurrent Booking Attempts** (15 minutes)
**Objective**: Test race condition prevention

**Test Steps**:
1. **Simulate Concurrent Bookings**:
   - Open two browser tabs with SQL Editor
   - Try to book same slot simultaneously
   - Verify only one booking succeeds

2. **Test Booking Limits**:
   - Create 4 bookings for same user (max limit)
   - Try to create 5th booking
   - Verify limit enforcement

**Expected Results**:
- Only one concurrent booking succeeds
- Booking limits enforced correctly
- Appropriate error messages shown

---

## 📊 **Test Results Documentation**

### **Test Execution Checklist**
```
Database Functionality:
[ ] Schema Validation - PASS/FAIL
[ ] Slot Generation - PASS/FAIL  
[ ] Booking Trigger - PASS/FAIL

Flutter Integration:
[ ] Basic Connectivity - PASS/FAIL
[ ] Availability Screen - PASS/FAIL
[ ] Real-time Updates - PASS/FAIL

ADR-007 Validation:
[ ] 3-Slot Offset Fix - PASS/FAIL
[ ] Provider Functionality - PASS/FAIL

Error Handling:
[ ] Connection Failures - PASS/FAIL
[ ] Concurrent Bookings - PASS/FAIL
```

### **Issue Tracking Template**
```
Issue ID: [AUTO_INCREMENT]
Test Suite: [Database/Flutter/ADR-007/Error]
Test Case: [Specific test name]
Severity: [Critical/High/Medium/Low]
Description: [What went wrong]
Expected: [What should happen]
Actual: [What actually happened]
Reproduction Steps: [How to reproduce]
Resolution: [How to fix]
Status: [Open/In Progress/Resolved]
```

---

## 🎯 **Success Criteria Summary**

### **Must Pass (Blocking)**
- ✅ All database schema components created successfully
- ✅ ADR-007 test suite passes (11/11 tests)
- ✅ 3-slot offset bug remains fixed
- ✅ Real-time streaming functional
- ✅ Flutter app connects and functions normally

### **Should Pass (Important)**
- ✅ Error handling works appropriately
- ✅ Concurrent booking prevention works
- ✅ Performance meets expectations (<2s for slot loading)

### **Nice to Have (Future)**
- ✅ Advanced analytics functions work
- ✅ Bulk operations perform well
- ✅ Monitoring and alerting configured

---

## 🔄 **Post-Testing Actions**

### **If All Tests Pass**
1. Update environment to production configuration
2. Deploy to staging environment
3. Conduct user acceptance testing
4. Plan production deployment

### **If Tests Fail**
1. Document all failures in issue tracker
2. Prioritize critical issues (ADR-007 functionality)
3. Fix issues and re-run affected tests
4. Consider rollback if critical failures persist

---

**Estimated Total Testing Time**: 2-3 hours  
**Critical Path**: Database → Flutter → ADR-007 → Production Ready  
**Next Steps**: Production deployment planning

**Last Updated**: June 20, 2025  
**Responsible**: Development Team
