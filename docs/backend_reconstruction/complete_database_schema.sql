-- =====================================================
-- COMPLETE SUPABASE DATABASE SCHEMA RECONSTRUCTION
-- =====================================================
-- Date: June 20, 2025
-- Purpose: Rebuild Supabase backend after infrastructure failure
-- ADR-007 Compatibility: Maintains 85% complete implementation
-- Critical: Preserves 3-slot offset bug fix validation

-- =====================================================
-- REQUIRED EXTENSIONS
-- =====================================================

-- Enable btree_gist extension for UUID support in GIST indexes
-- This is required for the overlapping booking prevention constraints
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- =====================================================
-- CORE TABLES (Foundation)
-- =====================================================

-- User profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Pitch settings and configuration
CREATE TABLE pitch_settings (
  id SERIAL PRIMARY KEY,
  pitch_name TEXT NOT NULL,
  open_time TIME NOT NULL,
  close_time TIME NOT NULL,
  slot_duration_minutes INTEGER NOT NULL,
  cancellation_window_hours INTEGER NOT NULL,
  price_per_hour DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  max_bookings_per_user INTEGER NOT NULL DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bookings table with race condition prevention
CREATE TABLE bookings (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  pitch_id INTEGER REFERENCES pitch_settings(id) NOT NULL,
  slot_start_time TIMESTAMPTZ NOT NULL,
  slot_end_time TIMESTAMPTZ NOT NULL,
  booking_date DATE NOT NULL, -- Will be set via trigger instead of generated column
  status TEXT DEFAULT 'confirmed' CHECK (status IN ('pendingPayment', 'confirmed', 'cancelledByUser', 'cancelledByAdmin', 'completed', 'noShow')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Prevent overlapping bookings for same user (using btree_gist extension)
  CONSTRAINT no_overlapping_user_bookings EXCLUDE USING gist (
    user_id WITH =,
    tstzrange(slot_start_time, slot_end_time, '[)') WITH &&
  ),

  -- Prevent double booking same slot
  CONSTRAINT no_double_booking EXCLUDE USING gist (
    pitch_id WITH =,
    tstzrange(slot_start_time, slot_end_time, '[)') WITH &&
  ) WHERE (status != 'cancelledByUser' AND status != 'cancelledByAdmin')
);

-- =====================================================
-- ADR-007 TIME SLOTS TABLE (Critical for 3-slot fix)
-- =====================================================

CREATE TABLE time_slots (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Slot scheduling information
  pitch_id INTEGER NOT NULL REFERENCES pitch_settings(id) ON DELETE CASCADE,
  slot_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  
  -- Slot status and booking (ADR-007 core functionality)
  is_booked BOOLEAN NOT NULL DEFAULT FALSE,
  is_available BOOLEAN NOT NULL DEFAULT TRUE, -- Manual override capability
  booking_id INTEGER NULL REFERENCES bookings(id) ON DELETE SET NULL,
  
  -- Pricing information
  price_per_hour DECIMAL(10,2) NOT NULL,
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_time_range CHECK (start_time < end_time),
  CONSTRAINT valid_slot_date CHECK (slot_date >= CURRENT_DATE - INTERVAL '1 day'),
  CONSTRAINT valid_price CHECK (price_per_hour >= 0)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE (ADR-007 Requirements)
-- =====================================================

-- Primary query pattern: fetch slots by pitch and date
CREATE INDEX idx_time_slots_pitch_date ON time_slots(pitch_id, slot_date);

-- Query available slots only
CREATE INDEX idx_time_slots_available ON time_slots(pitch_id, slot_date, is_available) 
WHERE is_available = TRUE;

-- Query booked slots for analytics
CREATE INDEX idx_time_slots_booked ON time_slots(pitch_id, slot_date, is_booked) 
WHERE is_booked = TRUE;

-- Fast lookup by booking ID
CREATE INDEX idx_time_slots_booking ON time_slots(booking_id) 
WHERE booking_id IS NOT NULL;

-- Unique constraint to prevent duplicate slots
CREATE UNIQUE INDEX idx_time_slots_unique ON time_slots(pitch_id, slot_date, start_time);

-- Time-based queries for cleanup/maintenance
CREATE INDEX idx_time_slots_date ON time_slots(slot_date);

-- Booking table indexes
CREATE INDEX idx_bookings_user_id ON bookings(user_id);
CREATE INDEX idx_bookings_pitch_date ON bookings(pitch_id, booking_date);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_time_range ON bookings USING gist (tstzrange(slot_start_time, slot_end_time));

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS for all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE pitch_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_slots ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Pitch settings policies (public read access)
CREATE POLICY "Anyone can view pitch settings" ON pitch_settings
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can manage pitch settings" ON pitch_settings
  FOR ALL USING (auth.role() = 'authenticated');

-- Bookings policies
CREATE POLICY "Users can view their own bookings" ON bookings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own bookings" ON bookings  
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own bookings" ON bookings
  FOR UPDATE USING (auth.uid() = user_id);

-- Time slots policies (ADR-007 requirements)
CREATE POLICY "Anyone can view time slots" ON time_slots
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can manage slots" ON time_slots
  FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- TRIGGER FUNCTIONS (ADR-007 Critical)
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Apply updated_at triggers to all tables
CREATE TRIGGER profiles_updated_at_trigger
  BEFORE UPDATE ON profiles
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER pitch_settings_updated_at_trigger
  BEFORE UPDATE ON pitch_settings
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER bookings_updated_at_trigger
  BEFORE UPDATE ON bookings
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER time_slots_updated_at_trigger
  BEFORE UPDATE ON time_slots
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to set booking_date from slot_start_time
CREATE OR REPLACE FUNCTION set_booking_date()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Set booking_date to the date portion of slot_start_time
  NEW.booking_date = NEW.slot_start_time::DATE;
  RETURN NEW;
END;
$$;

-- Trigger to automatically set booking_date
CREATE TRIGGER set_booking_date_trigger
  BEFORE INSERT OR UPDATE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION set_booking_date();

-- =====================================================
-- ADR-007 SLOT BOOKING STATUS TRIGGER (CRITICAL)
-- =====================================================

CREATE OR REPLACE FUNCTION update_slot_booking_status()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
DECLARE
  affected_rows INTEGER;
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Mark slot as booked when booking is created
    UPDATE time_slots 
    SET 
      is_booked = TRUE, 
      booking_id = NEW.id, 
      updated_at = NOW()
    WHERE pitch_id = NEW.pitch_id 
      AND slot_date = NEW.booking_date
      AND start_time = NEW.slot_start_time::TIME
      AND end_time = NEW.slot_end_time::TIME;
      
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log warning if no slot was found (but don't fail)
    IF affected_rows = 0 THEN
      RAISE WARNING 'No matching slot found for booking % (pitch: %, date: %, time: % - %)', 
        NEW.id, NEW.pitch_id, NEW.booking_date, 
        NEW.slot_start_time::TIME, NEW.slot_end_time::TIME;
    END IF;
    
    RETURN NEW;
    
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle status changes
    IF OLD.status != NEW.status THEN
      IF NEW.status IN ('cancelledByUser', 'cancelledByAdmin') THEN
        -- Mark slot as available when booking is cancelled
        UPDATE time_slots 
        SET 
          is_booked = FALSE, 
          booking_id = NULL, 
          updated_at = NOW()
        WHERE booking_id = NEW.id;
      ELSIF OLD.status IN ('cancelledByUser', 'cancelledByAdmin') AND NEW.status = 'confirmed' THEN
        -- Mark slot as booked when booking is reconfirmed
        UPDATE time_slots 
        SET 
          is_booked = TRUE, 
          booking_id = NEW.id, 
          updated_at = NOW()
        WHERE pitch_id = NEW.pitch_id 
          AND slot_date = NEW.booking_date
          AND start_time = NEW.slot_start_time::TIME
          AND end_time = NEW.slot_end_time::TIME;
      END IF;
    END IF;
    
    RETURN NEW;
    
  ELSIF TG_OP = 'DELETE' THEN
    -- Mark slot as available when booking is deleted
    UPDATE time_slots 
    SET 
      is_booked = FALSE, 
      booking_id = NULL, 
      updated_at = NOW()
    WHERE booking_id = OLD.id;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$;

-- Create the critical trigger for ADR-007
CREATE TRIGGER booking_slot_status_trigger
  AFTER INSERT OR UPDATE OR DELETE ON bookings
  FOR EACH ROW 
  EXECUTE FUNCTION update_slot_booking_status();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE time_slots IS 'ADR-007: Stores all time slots with pre-calculated booking status to fix 3-slot offset bug';
COMMENT ON COLUMN time_slots.pitch_id IS 'References the pitch this slot belongs to';
COMMENT ON COLUMN time_slots.slot_date IS 'The date of the slot (date only, no time)';
COMMENT ON COLUMN time_slots.start_time IS 'Start time of the slot (time only, no date)';
COMMENT ON COLUMN time_slots.end_time IS 'End time of the slot (time only, no date)';
COMMENT ON COLUMN time_slots.is_booked IS 'TRUE when slot is booked, automatically managed by triggers';
COMMENT ON COLUMN time_slots.is_available IS 'Manual override to disable specific slots';
COMMENT ON COLUMN time_slots.booking_id IS 'ID of the booking if slot is booked, NULL otherwise';
COMMENT ON COLUMN time_slots.price_per_hour IS 'Price for this slot, copied from pitch settings at creation';

COMMENT ON FUNCTION update_slot_booking_status IS 'ADR-007: Trigger function to automatically update slot booking status when bookings change';

-- =====================================================
-- ADR-007 SLOT GENERATION FUNCTIONS (CRITICAL)
-- =====================================================

CREATE OR REPLACE FUNCTION generate_time_slots(
  p_pitch_id INTEGER,
  p_date DATE,
  p_force_regenerate BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
  slots_created INTEGER,
  message TEXT,
  execution_time_ms INTEGER
)
LANGUAGE plpgsql
AS $$
DECLARE
  pitch_settings RECORD;
  current_time TIME;
  slot_duration INTERVAL;
  slots_count INTEGER := 0;
  start_time TIMESTAMP;
  existing_count INTEGER;
BEGIN
  start_time := clock_timestamp();

  -- Validate inputs
  IF p_date < CURRENT_DATE THEN
    RETURN QUERY SELECT 0, 'Cannot generate slots for past dates', 0;
    RETURN;
  END IF;

  -- Check if slots already exist (unless force regenerate)
  IF NOT p_force_regenerate THEN
    SELECT COUNT(*) INTO existing_count
    FROM time_slots
    WHERE pitch_id = p_pitch_id AND slot_date = p_date;

    IF existing_count > 0 THEN
      RETURN QUERY SELECT
        0,
        format('Slots already exist for pitch %s on %s (%s slots)', p_pitch_id, p_date, existing_count),
        EXTRACT(MILLISECONDS FROM clock_timestamp() - start_time)::INTEGER;
      RETURN;
    END IF;
  ELSE
    -- Delete existing slots if force regenerate
    DELETE FROM time_slots
    WHERE pitch_id = p_pitch_id AND slot_date = p_date;
  END IF;

  -- Get pitch settings
  SELECT id, open_time, close_time, slot_duration_minutes, price_per_hour
  INTO pitch_settings
  FROM pitch_settings
  WHERE id = p_pitch_id;

  IF NOT FOUND THEN
    RETURN QUERY SELECT 0, format('Pitch %s not found', p_pitch_id), 0;
    RETURN;
  END IF;

  -- Calculate slot duration
  slot_duration := make_interval(mins => pitch_settings.slot_duration_minutes);
  current_time := pitch_settings.open_time;

  -- Generate slots
  WHILE current_time + slot_duration <= pitch_settings.close_time LOOP
    INSERT INTO time_slots (
      pitch_id,
      slot_date,
      start_time,
      end_time,
      price_per_hour,
      is_booked,
      is_available
    ) VALUES (
      p_pitch_id,
      p_date,
      current_time,
      current_time + slot_duration,
      pitch_settings.price_per_hour,
      FALSE,
      TRUE
    );

    slots_count := slots_count + 1;
    current_time := current_time + slot_duration;
  END LOOP;

  RETURN QUERY SELECT
    slots_count,
    format('Generated %s slots for pitch %s on %s', slots_count, p_pitch_id, p_date),
    EXTRACT(MILLISECONDS FROM clock_timestamp() - start_time)::INTEGER;
END;
$$;

-- Bulk generate slots for multiple days
CREATE OR REPLACE FUNCTION generate_slots_bulk(
  p_pitch_id INTEGER,
  p_start_date DATE,
  p_end_date DATE,
  p_force_regenerate BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
  date_processed DATE,
  slots_created INTEGER,
  message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
  current_date DATE;
  result_record RECORD;
BEGIN
  -- Validate input dates
  IF p_start_date > p_end_date THEN
    RETURN QUERY SELECT NULL::DATE, 0, 'Error: Start date must be before end date';
    RETURN;
  END IF;

  IF p_end_date > CURRENT_DATE + INTERVAL '365 days' THEN
    RETURN QUERY SELECT NULL::DATE, 0, 'Error: Cannot generate slots more than 1 year in advance';
    RETURN;
  END IF;

  current_date := p_start_date;

  WHILE current_date <= p_end_date LOOP
    -- Generate slots for current date
    SELECT * INTO result_record
    FROM generate_time_slots(p_pitch_id, current_date, p_force_regenerate);

    RETURN QUERY SELECT current_date, result_record.slots_created, result_record.message;

    current_date := current_date + INTERVAL '1 day';
  END LOOP;
END;
$$;

-- Utility function to clean up old slots
CREATE OR REPLACE FUNCTION cleanup_old_slots(
  p_days_to_keep INTEGER DEFAULT 30
)
RETURNS TABLE(
  deleted_count INTEGER,
  message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
  cutoff_date DATE;
  deleted_rows INTEGER;
BEGIN
  cutoff_date := CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep;

  DELETE FROM time_slots
  WHERE slot_date < cutoff_date
    AND is_booked = FALSE; -- Only delete unbooked slots

  GET DIAGNOSTICS deleted_rows = ROW_COUNT;

  RETURN QUERY SELECT
    deleted_rows,
    format('Deleted %s old unbooked slots before %s', deleted_rows, cutoff_date);
END;
$$;

-- Analytics function for slot statistics
CREATE OR REPLACE FUNCTION get_slot_stats(
  p_pitch_id INTEGER DEFAULT NULL,
  p_date_from DATE DEFAULT CURRENT_DATE,
  p_date_to DATE DEFAULT CURRENT_DATE + INTERVAL '7 days'
)
RETURNS TABLE(
  total_slots BIGINT,
  available_slots BIGINT,
  booked_slots BIGINT,
  utilization_percentage NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_slots,
    COUNT(*) FILTER (WHERE NOT is_booked AND is_available) as available_slots,
    COUNT(*) FILTER (WHERE is_booked) as booked_slots,
    CASE
      WHEN COUNT(*) > 0 THEN
        ROUND((COUNT(*) FILTER (WHERE is_booked)::NUMERIC / COUNT(*)::NUMERIC) * 100, 2)
      ELSE 0
    END as utilization_percentage
  FROM time_slots
  WHERE (p_pitch_id IS NULL OR pitch_id = p_pitch_id)
    AND slot_date BETWEEN p_date_from AND p_date_to;
END;
$$;

-- =====================================================
-- FUNCTION COMMENTS
-- =====================================================

COMMENT ON FUNCTION generate_time_slots IS 'ADR-007: Generates time slots for a specific pitch and date based on pitch settings';
COMMENT ON FUNCTION generate_slots_bulk IS 'ADR-007: Utility to generate slots for multiple days at once';
COMMENT ON FUNCTION cleanup_old_slots IS 'Maintenance function to remove old unbooked slots';
COMMENT ON FUNCTION get_slot_stats IS 'Analytics function to get slot utilization statistics';

-- =====================================================
-- SCHEMA VALIDATION
-- =====================================================

-- Verify critical tables exist
DO $$
BEGIN
  -- Verify time_slots table exists (ADR-007 requirement)
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables
                 WHERE table_name = 'time_slots') THEN
    RAISE EXCEPTION 'CRITICAL: time_slots table was not created properly';
  END IF;

  -- Verify booking trigger exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.triggers
                 WHERE trigger_name = 'booking_slot_status_trigger') THEN
    RAISE EXCEPTION 'CRITICAL: booking_slot_status_trigger was not created';
  END IF;

  -- Verify ADR-007 functions exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.routines
                 WHERE routine_name = 'generate_time_slots') THEN
    RAISE EXCEPTION 'CRITICAL: generate_time_slots function was not created';
  END IF;

  RAISE NOTICE 'Schema validation passed - ADR-007 requirements met';
END;
$$;
