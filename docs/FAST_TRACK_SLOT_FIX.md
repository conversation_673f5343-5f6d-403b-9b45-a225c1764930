# Fast-Track Database Slot Management Implementation

**URGENT BUG FIX**: Fix 3-slot offset in availability display  
**Timeline**: 2-4 hours  
**Date**: 2025-06-18  

## 🚨 Quick Implementation Plan

### Step 1: Database Schema (30 minutes)

#### Create time_slots table
```sql
-- Create the time_slots table
CREATE TABLE time_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pitch_id UUID REFERENCES pitches(id) ON DELETE CASCADE,
  slot_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_booked BOOLEAN DEFAULT FALSE,
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  price_per_hour DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Essential indexes
CREATE INDEX idx_time_slots_pitch_date ON time_slots(pitch_id, slot_date);
CREATE INDEX idx_time_slots_booking ON time_slots(booking_id) WHERE booking_id IS NOT NULL;
CREATE UNIQUE INDEX idx_time_slots_unique ON time_slots(pitch_id, slot_date, start_time);
```

#### Create slot generation function
```sql
-- Function to generate slots for a pitch/date
CREATE OR REPLACE FUNCTION generate_time_slots(
  p_pitch_id UUID,
  p_date DATE
) RETURNS INTEGER AS $$
DECLARE
  pitch_settings RECORD;
  current_time TIME;
  slot_duration INTERVAL;
  slots_count INTEGER := 0;
BEGIN
  -- Get pitch settings
  SELECT opening_time, closing_time, slot_duration_minutes, price_per_hour
  INTO pitch_settings
  FROM pitches 
  WHERE id = p_pitch_id;
  
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- Delete existing slots for this date (if any)
  DELETE FROM time_slots WHERE pitch_id = p_pitch_id AND slot_date = p_date;
  
  -- Generate slots
  slot_duration := make_interval(mins => pitch_settings.slot_duration_minutes);
  current_time := pitch_settings.opening_time;
  
  WHILE current_time + slot_duration <= pitch_settings.closing_time LOOP
    INSERT INTO time_slots (
      pitch_id, slot_date, start_time, end_time, price_per_hour
    ) VALUES (
      p_pitch_id, p_date, current_time, current_time + slot_duration, pitch_settings.price_per_hour
    );
    
    slots_count := slots_count + 1;
    current_time := current_time + slot_duration;
  END LOOP;
  
  RETURN slots_count;
END;
$$ LANGUAGE plpgsql;
```

#### Create booking trigger
```sql
-- Function to update slot status when bookings change
CREATE OR REPLACE FUNCTION update_slot_booking_status()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Mark slot as booked
    UPDATE time_slots 
    SET is_booked = TRUE, booking_id = NEW.id, updated_at = NOW()
    WHERE pitch_id = NEW.pitch_id 
      AND slot_date = NEW.booking_date
      AND start_time = NEW.slot_start_time::TIME
      AND end_time = NEW.slot_end_time::TIME;
    RETURN NEW;
    
  ELSIF TG_OP = 'DELETE' THEN
    -- Mark slot as available
    UPDATE time_slots 
    SET is_booked = FALSE, booking_id = NULL, updated_at = NOW()
    WHERE booking_id = OLD.id;
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER booking_slot_status_trigger
  AFTER INSERT OR DELETE ON bookings
  FOR EACH ROW EXECUTE FUNCTION update_slot_booking_status();
```

### Step 2: Data Migration (30 minutes)

```sql
-- Generate slots for next 7 days for all pitches
DO $$
DECLARE
  pitch_record RECORD;
  date_record DATE;
  total_slots INTEGER := 0;
BEGIN
  FOR pitch_record IN SELECT id FROM pitches LOOP
    FOR date_record IN 
      SELECT generate_series(CURRENT_DATE, CURRENT_DATE + INTERVAL '6 days', '1 day')::DATE
    LOOP
      total_slots := total_slots + generate_time_slots(pitch_record.id, date_record);
    END LOOP;
  END LOOP;
  
  RAISE NOTICE 'Generated % total slots', total_slots;
END;
$$;

-- Update slot status based on existing bookings
UPDATE time_slots 
SET is_booked = TRUE, booking_id = b.id, updated_at = NOW()
FROM bookings b
WHERE time_slots.pitch_id = b.pitch_id
  AND time_slots.slot_date = b.booking_date
  AND time_slots.start_time = b.slot_start_time::TIME
  AND time_slots.end_time = b.slot_end_time::TIME;

-- Verify the data
SELECT 
  'Total slots' as metric, COUNT(*) as count FROM time_slots
UNION ALL
SELECT 
  'Booked slots' as metric, COUNT(*) as count FROM time_slots WHERE is_booked = TRUE
UNION ALL
SELECT 
  'Available slots' as metric, COUNT(*) as count FROM time_slots WHERE is_booked = FALSE;
```

### Step 3: Flutter Implementation (60 minutes)

#### Create new database provider
Create file: `lib/features/availability/application/database_slot_provider.dart`

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/time_slot_info_model.dart';

class DatabaseSlotProvider extends StateNotifier<AsyncValue<List<TimeSlotInfo>>> {
  final SupabaseClient _supabase = Supabase.instance.client;

  DatabaseSlotProvider() : super(const AsyncValue.loading());

  Stream<List<TimeSlotInfo>> watchSlots(String pitchId, DateTime date) {
    return _supabase
        .from('time_slots')
        .stream(primaryKey: ['id'])
        .eq('pitch_id', pitchId)
        .eq('slot_date', date.toIso8601String().split('T')[0])
        .order('start_time')
        .map((data) => data
            .map((json) => TimeSlotInfo.fromDatabaseJson(json))
            .toList());
  }

  Future<void> generateSlotsIfNeeded(String pitchId, DateTime date) async {
    try {
      // Check if slots exist for this date
      final existing = await _supabase
          .from('time_slots')
          .select('id')
          .eq('pitch_id', pitchId)
          .eq('slot_date', date.toIso8601String().split('T')[0])
          .limit(1);

      if (existing.isEmpty) {
        // Generate slots for this date
        await _supabase.rpc('generate_time_slots', params: {
          'p_pitch_id': pitchId,
          'p_date': date.toIso8601String().split('T')[0],
        });
      }
    } catch (error) {
      // Ignore errors, fallback to current system
    }
  }
}

// Provider
final databaseSlotProvider = StateNotifierProvider.family<
    DatabaseSlotProvider, 
    AsyncValue<List<TimeSlotInfo>>, 
    String
>((ref, pitchId) => DatabaseSlotProvider());

// Feature flag
final useDatabaseSlotsProvider = StateProvider<bool>((ref) => false);
```

#### Update TimeSlotInfo model
Update `lib/features/availability/domain/time_slot_info_model.dart`:

```dart
// Add this factory method to existing TimeSlotInfo class
factory TimeSlotInfo.fromDatabaseJson(Map<String, dynamic> json) {
  final date = DateTime.parse(json['slot_date']);
  final startTimeParts = (json['start_time'] as String).split(':');
  final endTimeParts = (json['end_time'] as String).split(':');
  
  final startTime = DateTime(
    date.year, date.month, date.day,
    int.parse(startTimeParts[0]),
    int.parse(startTimeParts[1]),
  );
  
  final endTime = DateTime(
    date.year, date.month, date.day,
    int.parse(endTimeParts[0]),
    int.parse(endTimeParts[1]),
  );

  return TimeSlotInfo(
    startTime: startTime,
    endTime: endTime,
    isBooked: json['is_booked'] ?? false,
    price: (json['price_per_hour'] ?? 0.0).toDouble(),
  );
}
```

#### Update availability_details.dart
Add hybrid provider to `availability_details.dart`:

```dart
// Add this method to AvailabilityDetails class
Widget _buildHybridSlotView(BuildContext context, WidgetRef ref) {
  final useDatabaseSlots = ref.watch(useDatabaseSlotsProvider);
  
  if (useDatabaseSlots) {
    // Use database slots
    return StreamBuilder<List<TimeSlotInfo>>(
      stream: ref.read(databaseSlotProvider(selectedPitchId.toString()).notifier)
          .watchSlots(selectedPitchId.toString(), selectedDate),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return _buildSlotList(snapshot.data!);
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  } else {
    // Use current system (fallback)
    return ref.watch(
      real_time.realTimeAvailabilityProvider(
        real_time.RealTimeAvailabilityParams(
          date: selectedDate,
          pitchId: selectedPitchId,
        ),
      ),
    ).when(
      data: (slots) => _buildSlotList(slots),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(child: Text('Error: $error')),
    );
  }
}

// Update build method to use hybrid view
@override
Widget build(BuildContext context, WidgetRef ref) {
  return ref.watch(pitchSettingsProvider(selectedPitchId)).when(
    data: (settings) {
      // Generate slots if using database approach
      final useDatabaseSlots = ref.watch(useDatabaseSlotsProvider);
      if (useDatabaseSlots) {
        ref.read(databaseSlotProvider(selectedPitchId.toString()).notifier)
            .generateSlotsIfNeeded(selectedPitchId.toString(), selectedDate);
      }
      
      return Column(
        children: [
          // ...existing header code...
          Expanded(child: _buildHybridSlotView(context, ref)),
        ],
      );
    },
    loading: () => const Center(child: CircularProgressIndicator()),
    error: (err, _) => Center(child: Text('Error: $err')),
  );
}
```

### Step 4: Testing & Rollout (30 minutes)

#### Test the feature flag
Add this to your main.dart or a debug screen:

```dart
// Debug toggle for testing
class DatabaseSlotToggle extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final useDatabaseSlots = ref.watch(useDatabaseSlotsProvider);
    
    return SwitchListTile(
      title: Text('Use Database Slots'),
      subtitle: Text('Toggle between old and new slot system'),
      value: useDatabaseSlots,
      onChanged: (value) {
        ref.read(useDatabaseSlotsProvider.notifier).state = value;
      },
    );
  }
}
```

#### Validation checklist
- [ ] Database slots generate correctly for all pitches
- [ ] Existing bookings show as booked in database slots  
- [ ] New bookings automatically update slot status
- [ ] Feature flag switches between systems correctly
- [ ] 3-slot offset issue is resolved in database mode
- [ ] Real-time updates still work

#### Emergency rollback
If issues found:
```dart
// Set feature flag to false
ref.read(useDatabaseSlotsProvider.notifier).state = false;
```

## Success Validation

### Before/After Comparison
1. **Create a booking** using current system
2. **Note which slot appears booked** (should be wrong - 3 slots off)
3. **Enable database slots** with feature flag
4. **Verify correct slot** shows as booked
5. **Create new booking** and verify immediate status update

### Performance Check
- Slot loading time should be <100ms
- Real-time updates should still work
- No errors in console/logs

## Rollout Strategy

1. **Start with feature flag OFF** (current system)
2. **Test with developer accounts** first
3. **Enable for 10% of users** if tests pass
4. **Full rollout** if no issues reported
5. **Remove feature flag** after 24-48 hours

---

**Total Implementation Time**: 2-4 hours  
**Risk Level**: Low (easy rollback via feature flag)  
**Expected Result**: 3-slot offset bug completely fixed
